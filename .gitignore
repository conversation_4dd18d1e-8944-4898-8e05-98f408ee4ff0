# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
**/__pycache__/
**/*.pyc
**/*.pyo
# Ignorar archivos compilados de Python
*.pyc
__pycache__/


# Virtual environments
.env/
.venv/
env/
venv/
ENV/
env.bak/
venv.bak/

# VSCode settings (opcional)
.vscode/

# macOS / Linux
.DS_Store
Thumbs.db

# Python egg files
*.egg
*.egg-info/
dist/
build/
.eggs/

# Logs
*.log

# Pipenv
Pipfile.lock

# MyPy
.mypy_cache/

# Pyre
.pyre/

# Coverage reports
htmlcov/
.coverage
.cache
embedding-service/app/__pycache__