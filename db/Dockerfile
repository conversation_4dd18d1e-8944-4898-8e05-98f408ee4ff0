FROM postgres:16

# Install pgvector extension
RUN apt-get update && apt-get install -y postgresql-server-dev-16 build-essential git \
  && git clone --branch v0.5.1 https://github.com/pgvector/pgvector.git \
  && cd pgvector && make && make install \
  && cd .. && rm -rf pgvector

# Copy initialization SQL script
COPY init.sql /docker-entrypoint-initdb.d/init.sql

# Copy CSV data file
COPY output.csv /docker-entrypoint-initdb.d/output.csv