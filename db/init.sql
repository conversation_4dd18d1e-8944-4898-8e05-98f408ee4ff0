CREATE EXTENSION IF NOT EXISTS vector;

CREATE TABLE knowledge_base (
    id TEXT PRIMARY KEY,
    embedding vector(1024), -- adjust dimension according to your vectors
    chunks TEXT,
    metadata TEXT,
    page INTEGER,
    type TEXT,
    place TEXT,
    filename TEXT
);

-- -- Load data from CSV
-- -- Ensure CSV columns are in the correct order
COPY knowledge_base(embedding, chunks, metadata, page, type, place, filename)
FROM '/docker-entrypoint-initdb.d/output.csv'
WITH (FORMAT csv, HEADER true);
