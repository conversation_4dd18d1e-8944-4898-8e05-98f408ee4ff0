#!/usr/bin/env python3
"""
Script para debuggear el endpoint /rag específicamente
"""

import asyncio
import httpx
import json

# Configuración
EMBEDDING_SERVICE_URL = "http://localhost:8001"

# Payload exacto que está enviando el logic-service
RAG_PAYLOAD = {
    "kb_id": "KGESVB3EJX",
    "top_k": 5,
    "local_document_name": "",
    "region": "eu-west-1",
    "query": "Quien es sancho panza",
    "model_id": "amazon.titan-embed-text-v2:0",
    "kb_type": "aws",
    "postgres_config": None,
    "filters": {}
}

async def test_rag_endpoint():
    """Prueba el endpoint /rag exactamente como lo hace el logic-service"""
    print("🔍 Probando endpoint /rag...")
    
    async with httpx.AsyncClient() as client:
        try:
            # Primero verificar que el servicio esté corriendo
            print(f"1. Verificando que el servicio esté corriendo en {EMBEDDING_SERVICE_URL}")
            health_response = await client.get(f"{EMBEDDING_SERVICE_URL}/docs", timeout=5.0)
            print(f"   Status: {health_response.status_code}")
            
            if health_response.status_code != 200:
                print("   ❌ El servicio no está corriendo o no responde")
                return False
            
            print("   ✅ El servicio está corriendo")
            
            # Ahora probar el endpoint /rag
            print(f"\n2. Probando POST {EMBEDDING_SERVICE_URL}/rag")
            print(f"   Payload: {json.dumps(RAG_PAYLOAD, indent=2)}")
            
            response = await client.post(
                f"{EMBEDDING_SERVICE_URL}/rag",
                json=RAG_PAYLOAD,
                timeout=30.0
            )
            
            print(f"   Status Code: {response.status_code}")
            print(f"   Headers: {dict(response.headers)}")
            
            if response.status_code == 200:
                data = response.json()
                results = data.get("retrievalResults", [])
                print(f"   ✅ Éxito! Resultados obtenidos: {len(results)}")
                return True
            else:
                print(f"   ❌ Error: {response.status_code}")
                print(f"   Response body: {response.text}")
                return False
                
        except Exception as e:
            print(f"   ❌ Error: {type(e).__name__}: {e}")
            return False

async def test_available_endpoints():
    """Lista todos los endpoints disponibles"""
    print("\n🔍 Verificando endpoints disponibles...")
    
    async with httpx.AsyncClient() as client:
        try:
            # Intentar obtener la documentación OpenAPI
            response = await client.get(f"{EMBEDDING_SERVICE_URL}/openapi.json", timeout=5.0)
            
            if response.status_code == 200:
                openapi_data = response.json()
                paths = openapi_data.get("paths", {})
                
                print("   Endpoints disponibles:")
                for path, methods in paths.items():
                    for method in methods.keys():
                        print(f"   - {method.upper()} {path}")
                
                return True
            else:
                print(f"   No se pudo obtener la documentación OpenAPI: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   Error obteniendo endpoints: {e}")
            return False

async def test_different_urls():
    """Prueba diferentes variaciones de la URL"""
    print("\n🔍 Probando diferentes variaciones de URL...")
    
    urls_to_try = [
        f"{EMBEDDING_SERVICE_URL}/rag",
        f"{EMBEDDING_SERVICE_URL}/api/rag",
        f"{EMBEDDING_SERVICE_URL}/v1/rag",
        f"{EMBEDDING_SERVICE_URL}/embedding/rag",
    ]
    
    async with httpx.AsyncClient() as client:
        for url in urls_to_try:
            try:
                print(f"   Probando: {url}")
                response = await client.post(url, json=RAG_PAYLOAD, timeout=5.0)
                print(f"   - Status: {response.status_code}")
                
                if response.status_code == 200:
                    print(f"   ✅ ¡Funciona! URL correcta: {url}")
                    return url
                elif response.status_code == 404:
                    print(f"   ❌ 404 Not Found")
                else:
                    print(f"   ⚠️  Respuesta inesperada: {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ Error: {type(e).__name__}")
    
    return None

async def main():
    print("🚀 Iniciando debug del endpoint /rag\n")
    
    # Prueba 1: Verificar endpoints disponibles
    await test_available_endpoints()
    
    # Prueba 2: Probar el endpoint /rag directamente
    success = await test_rag_endpoint()
    
    if not success:
        # Prueba 3: Probar diferentes URLs
        working_url = await test_different_urls()
        
        if working_url:
            print(f"\n✅ Encontrada URL que funciona: {working_url}")
            print("💡 Actualiza la configuración del logic-service para usar esta URL")
        else:
            print("\n❌ No se encontró ninguna URL que funcione")
            print("💡 Verifica que el embedding-service esté corriendo correctamente")
    else:
        print("\n🎉 El endpoint /rag funciona correctamente!")
        print("💡 El problema debe estar en otro lugar. Revisa los logs del logic-service.")

if __name__ == "__main__":
    asyncio.run(main())
