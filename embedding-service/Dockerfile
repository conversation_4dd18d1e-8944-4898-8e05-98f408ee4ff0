# Use an optimized Python base image
FROM python:3.12-slim-bookworm

# Install system dependencies (Poppler for pdf2image)
RUN apt-get update && apt-get install -y poppler-utils && rm -rf /var/lib/apt/lists/*

# Update pip and setuptools to latest versions
RUN pip install --no-cache-dir --upgrade pip setuptools

# Install Poetry package manager
RUN pip install poetry

# Set the working directory
WORKDIR /app

# Copy dependency files first to leverage Docker cache
COPY pyproject.toml poetry.lock ./

# Verify files were copied correctly (optional for debugging)
RUN ls -l /app

# Install dependencies before copying source code
RUN poetry install --no-root --no-interaction --no-ansi

# Copy the rest of the application code
COPY . .

# Expose application port
EXPOSE 5001

# Command to run the application
CMD ["poetry", "run", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8001", "--reload"]
