
# AWS Bedrock RAG System

## Project Overview
This project is an AWS Bedrock Retrieval-Augmented Generation (RAG) system. It leverages the capabilities of AWS services to build a robust and scalable RAG solution.

### Features
- Retrieval-augmented text generation
- Integration with AWS Bedrock
- Scalable architecture for handling large datasets

### Prerequisites
- Python 3.11+
- [Poetry](python-poetry.org/docs)
- AWS account with access to Bedrock services
- Required Python packages (see `Installation` section)
- keys to unlock secrets file, or your own credentials

## Installation
Clone the repository and reproduce the environment:
```bash
git clone https://insight-data.pl.s2-eu.capgemini.com/gitlab/pabldomi/bedrock-kb-test
cd bedrock-kb-test
poetry install
```

## Usage
- Unlock secrets file with `git-crypt unlock file_path`. Alternatively, use your own `.env` file.
- Activate virtual environment with `poetry shell`.
Run the main script in development mode with:
```bash
poetry run fastapi dev app/main.py
```
- Run tests with `poetry run pytest` (optionally with `-sv` for verbosity).

## AWS Configuration
Ensure your AWS credentials are configured correctly. Please check `AWSCLI-README.md` for more information.

---
---


## Project Structure

The project is structured as follows:

```
app/
├── tests/ # To store unit tests
│   └── test_health_controller.py
├── config/ # To store differentconfiguration files (e.g., .env)
│   ├── settings.py
│   ├── db_settings.py
│   └── app_settings.py
├── constants/ # To store constants or values used throughout the project
│   ├── constants.py
│   └── prompts.py
├── models/ # To store database pydantic models
│   ├── database.py
│   └── postgres.py
├── dto/ # To store data transfer objects
│   ├── file_dto.py
│   └── user_dto.py
├── routes/ # To store API routes
│   ├── __init__.py
│   ├── health_controller.py
│   ├── rag_controller.py
│   └── file_controller.py
├── services/ # To store classes that perform the business logic
│   ├── __init__.py
│   ├── health_service.py
│   ├── rag_service.py
│   └── file_service.py
├── __init__.py
└── main.py
```




## Contributing - Git Branch Naming Convention

To ensure a smooth workflow and clear organization of our Git branches, we follow a consistent naming convention. This helps us easily identify the purpose of each branch and improves collaboration across the team.

Branches should follow the format: `type/short-description`. The different values `type` can take are:

### 1. **Feature Branches (`feature/`)**
Feature branches are used to work on new features or major improvements to existing features.

- **Format**: `feature/<short-description>`
- **Example**: `feature/user-authentication`, `feature/product-page-layout`

### 2. **Fix Branches (`fix/`)**
Fix branches are used to address bugs or issues in the codebase.

- **Format**: `fix/<short-description>`
- **Example**: `fix/login-error`, `fix/cart-issue`

### 3. **Task Branches (`task/`)**
Task branches are for non-feature-related tasks such as code refactoring, updates to documentation, or administrative work.

- **Format**: `task/<short-description>`
- **Example**: `task/refactor-auth-service`, `task/update-readme`

### 4. **Test Branches (`test/`)**
Test branches are used for tasks related to testing, such as writing or running tests for specific features or the entire application.

- **Format**: `test/<short-description>`
- **Example**: `test/api-endpoints`, `test/payment-method-integration`

### 5. **Release Branches (`release/`)**
Release branches are used to prepare for a new production release. This is where we finalize the code for production deployment.

- **Format**: `release/<version-number>`
- **Example**: `release/1.0.0`, `release/2.3.5`

### General Branch Naming Guidelines
- **Short Descriptions**: Use short, descriptive names that clearly indicate the purpose of the branch.
- **Hyphenation**: Separate words with hyphens (`-`) for readability.
- **Lowercase**: Use lowercase letters for branch names.
- **No Special Characters**: Avoid spaces, slashes, or special characters in branch names except for the slash (`/`) that separates the prefix from the description.
