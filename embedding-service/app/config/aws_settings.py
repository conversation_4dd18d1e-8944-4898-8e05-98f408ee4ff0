from pydantic import Field
from pydantic_settings import BaseSettings
from functools import lru_cache
import boto3


@lru_cache
def get_boto3_session():
    """
    Creates and returns a cached boto3 session with SSO credentials.
    Returns None if session creation fails.
    """
    try:
        session = boto3.Session()
        return session
    except Exception as e:
        print(f"Error creating AWS session: {e}")
        return None


@lru_cache
def get_boto3_client(service_name: str, region_name: str = "eu-west-1"):
    session = get_boto3_session()
    return session.client(service_name, region_name=region_name)


class AWSSettings(BaseSettings):
    s3_bucket: str = Field(default="sourcedocumentationkb", alias="S3_BUCKET")
    aws_region: str = Field(default="eu-west-1", alias="AWS_REGION")
