import asyncpg
from langchain_community.vectorstores import PGVector
from langchain_community.embeddings import BedrockEmbeddings


class DBConnector:
    def __init__(
        self,
        db_host: str,
        db_port: int,
        db_user: str,
        db_password: str,
        db_name: str,
        embedding_model: str,
        aws_bedrock_region: str = "eu-west-1",
    ):
        self.db_host = db_host
        self.db_port = db_port
        self.db_user = db_user
        self.db_password = db_password
        self.db_name = db_name
        self.embedding_model = embedding_model
        self.aws_region = aws_bedrock_region

        self.pool = None
        self.vector_store = None

    async def connect(self):
        if not self.pool:
            self.pool = await asyncpg.create_pool(
                host=self.db_host,
                port=self.db_port,
                user=self.db_user,
                password=self.db_password,
                database=self.db_name,
                ssl=False,
            )

    async def close(self):
        if self.pool:
            await self.pool.close()
            self.pool = None
