from pydantic import BaseModel
from typing import Optional


class LLMConfigDTO(BaseModel):
    """
    Represents the configuration for LLM requests.
    """

    model_id: str = "anthropic.claude-3-sonnet-20240229-v1:0"
    anthropic_version: Optional[str] = None
    max_tokens: Optional[int] = 1000
    temperature: Optional[float] = 0


class PostgresQueryConfig(BaseModel):
    page_start: int = None
    page_end: int = None
    table_name: str = "knowledge_base"


class RAGQueryDTO(BaseModel):
    """
    DTO that combines Retrieval and Generation functionality.
    This DTO allows performing complete RAG queries in a single call.

    Attributes:
        kb_id (str): Knowledge Base identifier
        top_k (Optional[int]): Number of documents to retrieve from the KB (default: 5)
        local_document_name (Optional[str]): Name of the local document to query
    """

    kb_id: str = "KGESVB3EJX"
    top_k: Optional[int] = 5
    local_document_name: Optional[str] = ""
    region: str = "eu-west-1"
    query: str = "que es el boe?"
    model_id: str = "amazon.titan-embed-text-v2:0"
    kb_type: str = "aws"
    postgres_config: Optional[PostgresQueryConfig] = PostgresQueryConfig()
    filters: dict = {}


class IngestQueryDTO(BaseModel):
    """
    DTO for document ingestion configuration.

    Attributes:
        parsing_strategy_LLM_configuration (LLMConfigDTO): LLM configuration for parsing strategy
        bucket_name (str): S3 bucket name for data ingestion (default: "data-ingestion-cecabank")
        folder_raw_documents (str): Folder for raw documents (default: "raw_documents")
        folder_parsed_documents (str): Folder for parsed documents (default: "parsed")
        parsing_strategy_prompt (str): Filename for parsing strategy prompt (default: "parsing_strategy.txt")
        page_init (int): Initial page for parsing (default: 1)
    """

    parsing_strategy_LLM_configuration: LLMConfigDTO
    bucket_name: str = "cecabank-docs"
    object_key: Optional[str] = None
    folder_raw_documents: str = "raw"
    folder_parsed_documents: str = "parsed"
    parsing_strategy_prompt: str = "parsing_strategy.txt"
    batch: Optional[bool] = False
    page_init: int = 1

    class Config:
        arbitrary_types_allowed = True  # Remove Config if not needed


class EmbeddingQueryDTO(BaseModel):
    """
    DTO for embedding generation and retrieval operations.
    This DTO handles embedding-related queries and configurations.

    Attributes:
        model_id (str): Identifier for the embedding model
        region (str): AWS region for the embedding service
        query (str): Text to be embedded
        embedding_type (str): Type of embedding service to use
    """

    model_id: str = "amazon.titan-embed-text-v2:0"
    region: str = "eu-west-1"
    query: str = ""
    embedding_type: str = "aws"
