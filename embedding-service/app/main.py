import os
import logging
from fastapi import FastAPI
from app.routes import *
from contextlib import asynccontextmanager
from app.config.db_settings import DBConnector
from app.services.drivers.rag.postgres import RAGPostgres

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# @asynccontextmanager
# async def lifespan(app: FastAPI):
#     db_connection = DBConnector(
#     db_host=os.getenv("DB_HOST", "localhost"),
#     db_port=int(os.getenv("DB_PORT", 5432)),
#     db_user=os.getenv("DB_USER", "postgres"),
#     db_password=os.getenv("DB_PASSWORD", "abc123"),
#     db_name=os.getenv("DB_NAME", "postgres"),
#     embedding_model=os.getenv("EMBEDDING_MODEL", "amazon.titan-embed-text-v2:0"),
#     aws_bedrock_region=os.getenv("AWS_BEDROCK_REGION", "eu-west-1"),
#     )

#     await db_connection.connect()

#     app.state.db_connection = db_connection
#     app.state.rag_service = RAGPostgres(db_connection)
#     yield
#     await db_connection.close()

# Crear FastAPI app con lifespan
app = FastAPI(
    title="Embedding Service",
    version="0.1.0",
    description="This project is a server of embedding",
    # lifespan=lifespan,
)

# Routers
app.include_router(health_controller)
app.include_router(rag_controller)
app.include_router(ingest_controller)
app.include_router(embedding_controller)
