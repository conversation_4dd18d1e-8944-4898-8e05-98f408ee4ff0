from fastapi import APIRouter
from app.dtos.dto import EmbeddingQueryDTO
from app.services.drivers.embeddings.native.aws.embedding import AWSEmbedding
from app.utils.enums import EmbeddingTypes

# Initialize the API router
router = APIRouter()


@router.post("/embedding")
async def embedding(embedding_query: EmbeddingQueryDTO):
    """ """
    try:
        if embedding_query.embedding_type == EmbeddingTypes.AWS.value:
            embedding_service = AWSEmbedding(
                embedding_query.region, embedding_query.model_id
            )
            rag_response = await embedding_service.get_embedding(
                query=embedding_query.query
            )
            return rag_response
    except Exception as e:
        raise e
