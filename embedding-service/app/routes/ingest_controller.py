from app.services.drivers.ingest.native.aws import parsing_strategy
from app.dtos.dto import IngestQueryDTO
from fastapi import APIRouter

# Initialize FastAPI router for ingestion endpoints
router = APIRouter()


@router.post("/ingest/start")
async def ingest_start(
    rag_query: IngestQueryDTO,
):
    """
    Initiates the document ingestion process using the PDF to pgvector pipeline.

    Args:
        rag_query (IngestQueryDTO): Configuration for the document ingestion process,
            including bucket locations, parsing strategy, and LLM settings

    Returns:
        str: Confirmation message upon successful ingestion start
    """

    response = await parsing_strategy.pdf_to_pgvector_pipeline(
        bucket_name=rag_query.bucket_name,
        object_key=rag_query.object_key,
        folder_raw_documents=rag_query.folder_raw_documents,
        folder_parsed_documents=rag_query.folder_parsed_documents,
        parsing_strategy=rag_query.parsing_strategy_prompt,
        page_init=1,
        model_configuration=rag_query.parsing_strategy_LLM_configuration,
    )

    return "TOP"
