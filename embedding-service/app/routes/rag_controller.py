from fastapi import APIRouter, Request
from app.dtos.dto import RAGQueryDTO
from app.services.drivers.rag.bedrock import RAGBedrock
from app.utils.enums import RAGTypes
from app.services.drivers.rag.postgres import RAGPostgres

router = APIRouter()


@router.post("/rag")
async def rag(rag_query: RAGQueryDTO, request: Request):
    """
    Executes a RAG query using either AWS Bedrock or PostgreSQL as the knowledge base.
    """

    try:
        if rag_query.kb_type == RAGTypes.AWS.value:
            rag_service = RAGBedrock(kb_id=rag_query.kb_id, region=rag_query.region)
            rag_response = await rag_service.execute(
                user_query=rag_query.query,
                filename=rag_query.local_document_name,
                number_of_results=rag_query.top_k,
                filters = rag_query.filters
            )
            return rag_response

        elif rag_query.kb_type == RAGTypes.POSTGRES.value:
            # Verificar si rag_service está disponible en la aplicación
            if hasattr(request.app.state, "rag_service"):
                rag_service: RAGPostgres = request.app.state.rag_service
                rag_response = await rag_service.query(
                    query_text=rag_query.query,
                    top_k=rag_query.top_k,
                    filename=rag_query.local_document_name,
                    page_start=rag_query.postgres_config.page_start,
                    page_end=rag_query.postgres_config.page_end,
                    table_name=rag_query.postgres_config.table_name,
                )
                return rag_response
            else:
                return {
                    "error": "PostgreSQL RAG service not available - database connection is disabled"
                }

    except Exception as e:
        raise e
