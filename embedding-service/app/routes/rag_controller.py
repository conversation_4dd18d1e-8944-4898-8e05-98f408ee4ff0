from fastapi import APIRouter, Request
from app.dtos.dto import RAG<PERSON>ueryDTO
from app.services.drivers.rag.bedrock import RAGBedrock
from app.utils.enums import RAGTypes
from app.services.drivers.rag.postgres import RAGPostgres
import logging
import json

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

router = APIRouter()


@router.post("/rag")
async def rag(rag_query: RAGQueryDTO, request: Request):
    """
    Executes a RAG query using either AWS Bedrock or PostgreSQL as the knowledge base.
    """

    logger.info(f"RAG endpoint called with kb_type: {rag_query.kb_type}")
    logger.info(f"Query: '{rag_query.query}'")
    logger.info(f"KB ID: {rag_query.kb_id}")
    logger.info(f"Top K: {rag_query.top_k}")
    logger.info(f"Local document name: '{rag_query.local_document_name}'")

    try:
        if rag_query.kb_type == RAGTypes.AWS.value:
            logger.info("Using AWS Bedrock RAG service")
            rag_service = RAGBedrock(kb_id=rag_query.kb_id, region=rag_query.region)
            rag_response = await rag_service.execute(
                user_query=rag_query.query,
                filename=rag_query.local_document_name,
                number_of_results=rag_query.top_k,
                filters = rag_query.filters
            )
            logger.info(f"AWS Bedrock RAG response received with {len(rag_response.get('retrievalResults', []))} results")
            return rag_response

        elif rag_query.kb_type == RAGTypes.POSTGRES.value:
            # Verificar si rag_service está disponible en la aplicación
            if hasattr(request.app.state, "rag_service"):
                rag_service: RAGPostgres = request.app.state.rag_service

                # Handle case when postgres_config is None
                if rag_query.postgres_config is None:
                    from app.dtos.dto import PostgresQueryConfig
                    postgres_config = PostgresQueryConfig()
                else:
                    postgres_config = rag_query.postgres_config

                rag_response = await rag_service.query(
                    query_text=rag_query.query,
                    top_k=rag_query.top_k,
                    filename=rag_query.local_document_name,
                    page_start=postgres_config.page_start,
                    page_end=postgres_config.page_end,
                    table_name=postgres_config.table_name,
                )
                return rag_response
            else:
                return {
                    "error": "PostgreSQL RAG service not available - database connection is disabled"
                }

    except Exception as e:
        logger.error(f"Error in RAG endpoint: {type(e).__name__}: {str(e)}")
        raise e
