from ..base_embedding import BaseEmbedding
from langchain.embeddings import AzureOpenAIEmbeddings
from typing import List


class AzureLangchainEmbedding(BaseEmbedding):
    def __init__(self, api_key: str, endpoint: str, deployment_name: str):
        self.client = AzureOpenAIEmbeddings(
            azure_deployment=deployment_name,
            azure_endpoint=endpoint,
            api_key=api_key,
        )

    def get_embedding(self, query: str) -> List[float]:
        return self.client.get_embedding(query)
