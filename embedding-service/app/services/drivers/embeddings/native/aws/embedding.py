import boto3
import json
from app.services.drivers.embeddings.base_embedding import Base<PERSON>mbedding
from typing import List
import logging
from botocore.exceptions import BotoCoreError, NoCredentialsError, ClientError

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AWSEmbedding(BaseEmbedding):
    def __init__(self, region: str, model_id: str):
        """
        Initializes the AWS Bedrock Runtime client for generating embeddings.

        :param region: The AWS region where the service is located.
        :param model_id: The model identifier to use.
        """
        if not region or not isinstance(region, str):
            raise ValueError("The 'region' parameter must be a non-empty string.")
        if not model_id or not isinstance(model_id, str):
            raise ValueError("The 'model_id' parameter must be a non-empty string.")

        try:
            self.client = boto3.client("bedrock-runtime", region_name=region)
            self.model_id = model_id
        except (BotoCoreError, NoCredentialsError) as e:
            logger.error(f"Error initializing AWS client: {str(e)}")
            raise RuntimeError(
                "Could not connect to AWS. Please verify your credentials and connection."
            )

    async def get_embedding(self, query: str) -> List[float]:
        """
        Generates an embedding for the given text.

        :param query: The input text.
        :return: List of floats representing the embedding, or empty list if an error occurs.
        """
        if not query or not isinstance(query, str):
            logger.warning("The 'query' parameter must be a non-empty string.")
            return []

        try:
            body = json.dumps({"inputText": query})
            response = self.client.invoke_model(
                modelId=self.model_id,
                body=body,
                accept="application/json",
                contentType="application/json",
            )
            result = json.loads(response["body"].read())

            if "embedding" not in result:
                logger.error("AWS response does not contain 'embedding' key.")
                return []

            return result["embedding"]

        except ClientError as e:
            logger.error(f"AWS request error: {e.response['Error']['Message']}")
        except json.JSONDecodeError:
            logger.error("Error decoding AWS JSON response.")
        except (BotoCoreError, NoCredentialsError) as e:
            logger.error(f"AWS error: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error: {str(e)}")

        return []
