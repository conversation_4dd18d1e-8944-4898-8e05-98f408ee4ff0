import requests
from typing import List


class AzureEmbedding:
    def __init__(
        self,
        api_key: str,
        endpoint: str,
        deployment_name: str,
        api_version: str = "2023-05-15",
    ):
        self.api_key = api_key
        self.endpoint = endpoint.rstrip("/")
        self.deployment_name = deployment_name
        self.api_version = api_version

    def get_embedding(self, query: str) -> List[float]:
        url = (
            f"{self.endpoint}/openai/deployments/{self.deployment_name}/embeddings"
            f"?api-version={self.api_version}"
        )
        headers = {
            "Content-Type": "application/json",
            "api-key": self.api_key,
        }
        body = {"input": query}

        response = requests.post(url, headers=headers, json=body)
        response.raise_for_status()
        result = response.json()

        if "data" not in result or not result["data"]:
            raise ValueError("Invalid embedding response from Azure OpenAI API.")

        return result["data"][0]["embedding"]
