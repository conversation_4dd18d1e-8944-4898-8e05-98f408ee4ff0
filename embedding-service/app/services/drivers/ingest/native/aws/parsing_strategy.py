from pdf2image import convert_from_bytes
from app.config.aws_settings import get_boto3_client
from app.dtos.dto import LLMConfigDTO
from typing import List, Optional
import pdfplumber
import io
import logging
import json

"""
This file processes a set of PDF files from S3.
Process output consists of:
1. A .txt file containing the raw text
2. A separate JSON file containing metadata
Text extraction is performed using pdfplumber, and metadata is extracted using LLMs.
This file can be executed from any context (Lambda or FastAPI).
Note: It uses memory to load files, so processing multiple large files may require refactoring.
"""

logging.basicConfig(level=logging.INFO)
s3 = get_boto3_client("s3")
bedrock_runtime = get_boto3_client("bedrock-runtime")
prompt_paths = "app/services/drivers/ingest/native/aws/prompts"


def query_bedrock(
    system_prompt: str = "",
    content_messages: List[dict] = [],
    model_config: Optional[LLMConfigDTO] = None,
) -> Optional[str]:
    if model_config is None:
        logging.error("Model configuration must not be None.")
        raise ValueError("Model configuration is required")

    if not isinstance(content_messages, list) or not all(
        isinstance(m, dict) for m in content_messages
    ):
        logging.error("Invalid content_messages: expected list of dicts")
        raise TypeError("content_messages must be a list of dictionaries")

    try:
        response = bedrock_runtime.converse(
            modelId=model_config.model_id,
            messages=content_messages,
            system=[{"text": system_prompt}],
            inferenceConfig={
                "maxTokens": model_config.max_tokens,
                "temperature": model_config.temperature,
            },
        )

        output = response.get("output", {})
        message = output.get("message", {})
        content = message.get("content", [])

        if not isinstance(content, list):
            logging.warning(f"Unexpected content format: {content}")
            return None

        for msg in content:
            if isinstance(msg, dict) and "text" in msg:
                return msg["text"]

        logging.info("No text response found in Bedrock output")
        return None

    except Exception as e:
        logging.exception("Error querying Bedrock")
        raise RuntimeError("Bedrock query failed") from e


def build_message(
    curr_img: bytes,
    page_number: int,
    prev_response: Optional[str] = None,
    prev_img: Optional[bytes] = None,
) -> List[dict]:
    """
    Builds a structured message with images and text for LLM processing.

    Args:
        curr_img: Current page image in bytes format
        page_number: Current page number
        prev_response: Response generated from previous page (optional)
        prev_img: Previous page image in bytes format (optional)
    Returns:
        List containing a structured chat message for LLM
    """
    content = [
        {"text": f"This is the page to process: number {page_number}"},
        {"image": {"format": "png", "source": {"bytes": curr_img}}},
    ]

    if prev_response and prev_img:
        content.extend(
            [
                {"text": f"Output generated from previous page:\n{prev_response}"},
                {"image": {"format": "png", "source": {"bytes": prev_img}}},
            ]
        )

    return [{"role": "user", "content": content}]


def upload_file(bucket_name, folder_prefix, file_name, text):
    "Saves extracted text to a file in S3."
    if isinstance(text, dict):
        text = json.dumps(text, ensure_ascii=False, indent=4)
    content_bytes = text.encode("utf-8")
    content_buffer = io.BytesIO(content_bytes)
    s3_key = f"{folder_prefix}{file_name}"
    s3.upload_fileobj(content_buffer, bucket_name, s3_key)
    logging.info(f"File uploaded: s3://{bucket_name}/{s3_key}")


def extract_text_by_page(pdf_bytes: bytes) -> dict:
    """
    Extracts text from each PDF page and returns it as a dictionary.
    If a page has no text, includes a default message.
    """
    try:
        with pdfplumber.open(io.BytesIO(pdf_bytes)) as pdf:
            return {
                i
                + 1: (
                    page.extract_text() or "No text could be extracted from this page."
                )
                for i, page in enumerate(pdf.pages)
            }
    except Exception as e:
        logging.error(f"Error processing PDF: {e}")
        return {1: "Could not open or process the PDF file."}


def build_images_array(pdf_bytes: bytes) -> list:
    print("Building images print inside build_images_array")
    images = []
    try:
        with pdfplumber.open(io.BytesIO(pdf_bytes)) as pdf:
            num_pages = len(pdf.pages)
        batch_size = 10  # Adjust number of pages to process at a time memory wise

        for i in range(0, num_pages, batch_size):
            print(f"Processing pages {i+1} to {min(i+batch_size, num_pages)}")
            batch_images = convert_from_bytes(pdf_bytes, first_page=i+1, last_page=min(i+batch_size, num_pages))
            images.extend([_image_to_bytes(img) for img in batch_images])

        print(f"Successfully converted {len(images)} pages to images")
        return images
    except Exception as e:
        logging.error(f"Error converting PDF to images: {e}")
        return []


def _image_to_bytes(image) -> bytes:
    """Converts a PIL image to bytes in PNG format."""
    buffer = io.BytesIO()
    image.save(buffer, format="PNG")
    return buffer.getvalue()


def upload_txt(
    pdf_bytes: bytes, bucket_name: str, folder_parsed_documents: str, file_name: str
):
    """
    Extracts text from PDF and uploads each page as a .txt file to S3.
    """
    logging.info(f"Processing file: {file_name}")

    try:
        texts = extract_text_by_page(pdf_bytes)
        for page_num, text in texts.items():
            logging.info(f"Uploading page (txt): {page_num}")
            upload_file(
                bucket_name,
                f"{folder_parsed_documents}/txt/{file_name}/",
                f"{file_name}-{page_num}.txt",
                text,
            )
    except Exception as e:
        logging.error(f"Error processing and uploading text for file {file_name}: {e}")


def build_and_upload_metadata(
    parsing_strategy: str,
    images: list,
    file_name: str,
    bucket_name: str,
    folder_parsed_documents: str,
    page_init: int,
    model_config: LLMConfigDTO,
):
    """
    Processes PDF images, extracts metadata using Bedrock, and uploads to S3.
    """
    system_prompt = _load_system_prompt(parsing_strategy)
    if not system_prompt:
        return

    prev_response = None
    prev_img = None

    for page_number, curr_img in enumerate(images[page_init - 1 :], start=page_init):
        try:
            messages = build_message(curr_img, page_number, prev_response, prev_img)
            raw_response = query_bedrock(system_prompt, messages, model_config)
            logging.info(f"Response (page {page_number}): {raw_response}")

            parsed = _parse_bedrock_response(raw_response, file_name)
            if not parsed:
                continue

            upload_file(
                bucket_name,
                f"{folder_parsed_documents}/txt/{file_name}/",
                f"{file_name}-{page_number}.txt.metadata.json",
                parsed,
            )
            prev_img = curr_img
            prev_response = json.dumps(parsed)

        except Exception as e:
            logging.error(f"Error processing page {page_number}: {e}")
            continue


def _load_system_prompt(strategy_filename: str) -> str:
    try:
        with open(f"{prompt_paths}/{strategy_filename}", "r", encoding="utf-8") as f:
            return f.read()
    except Exception as e:
        logging.error(f"Could not read model prompt: {e}")
        return ""


def _parse_bedrock_response(response: str, file_name: str) -> dict | None:
    """
    Cleans, validates, and converts Bedrock response into a metadata JSON.
    """
    try:
        response = response.replace("\n", " ").strip()

        if not response.startswith("{"):
            response = "{" + response.split("{", 1)[-1]
        if not response.endswith("}"):
            response = response.rsplit("}", 1)[0] + "}"

        parsed = json.loads(response)

        if not isinstance(parsed, dict) or "metadataAttributes" not in parsed:
            logging.error(f"Invalid metadata structure: {parsed}")
            return None

        # Sanitize string values
        for key, value in parsed["metadataAttributes"].items():
            if isinstance(value, str):
                parsed["metadataAttributes"][key] = value.replace('"', '\\"').replace(
                    "\n", " "
                )

        parsed["metadataAttributes"]["filename"] = file_name
        return parsed

    except json.JSONDecodeError as e:
        logging.error(f"Error parsing JSON: {e}")
        logging.debug(f"Problematic response: {response}")
        return None


def list_and_load_pdfs(bucket_name: str, folder_prefix: str) -> list:
    """
    Lists PDF files in an S3 folder.

    Args:
        bucket_name: Name of the S3 bucket
        folder_prefix: Prefix (folder) within the bucket
    Returns:
        List of PDF file keys found
    """
    try:
        response = s3.list_objects_v2(Bucket=bucket_name, Prefix=folder_prefix)

        if "Contents" not in response:
            logging.info("No PDF files found.")
            return []

        pdf_files = [
            obj["Key"]
            for obj in response["Contents"]
            if obj["Key"].lower().endswith(".pdf")
        ]

        logging.info(f"Found {len(pdf_files)} PDF files.")
        return pdf_files

    except Exception as e:
        logging.error(f"Error listing files in S3: {e}")
        return []


async def pdf_to_pgvector_pipeline(
    bucket_name: str = None,
    object_key: str = None,
    folder_raw_documents: str = None,
    folder_parsed_documents: str = None,
    parsing_strategy: str = None,
    page_init: int = 1,
    model_configuration: LLMConfigDTO = None,
    batch: bool = False,
):
    """
    Main pipeline: processes PDF files from S3, extracts images and metadata,
    and uploads processed results to S3.
    """
    if batch:
        pdf_files = list_and_load_pdfs(bucket_name, folder_raw_documents)
    elif not batch and object_key != None:
        pdf_files = [object_key]
    if not pdf_files:
        logging.info("No files found to process.")
        return

    for pdf_key in pdf_files:
        try:
            logging.info(f"Processing file: {pdf_key}")
            file_name = pdf_key.split("/", 1)[-1]
            response = s3.get_object(Bucket=bucket_name, Key=pdf_key)
            pdf_bytes = response["Body"].read()
            upload_txt(
                pdf_bytes=pdf_bytes,
                bucket_name=bucket_name,
                folder_parsed_documents=folder_parsed_documents,
                file_name=file_name,
            )
            images = build_images_array(pdf_bytes)
            if not images:
                logging.warning(f"Could not generate images for {file_name}")
                continue
            build_and_upload_metadata(
                parsing_strategy=parsing_strategy,
                images=images,
                file_name=file_name,
                bucket_name=bucket_name,
                folder_parsed_documents=folder_parsed_documents,
                page_init=page_init,
                model_config=model_configuration,
            )

        except Exception as e:
            logging.error(f"Error in processing {pdf_key}: {e}")
            continue
