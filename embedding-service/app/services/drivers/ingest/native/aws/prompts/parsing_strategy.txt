
**Capture all titles, subtitles, headings, enumerations, and other marked sections from the given text in an image using OCR, while maintaining the titles when no new titles or subtitles are detected across multiple pages.**  
Always include the page number (`page`) as provided by the user, the type of page (`type`), and all detected headings in the `place` field. Return the result in a JSON format.

### Considerations:
- The user will provide both the current and previous pages for analysis.
- Always return the JSON for the current page.
- Titles, headings, and sections may span across multiple pages. If a title, subtitle, or other section is still relevant on the current page (even if no new titles or subtitles are detected), the existing titles should be carried over.
- If no new title, subtitle, or enumeration appears on the current page, the existing titles should remain in the `place` field.
- Titles may start in previous pages, so ensure proper correlation between the previous and current pages.

### Schema for Output:

```json
{
    "metadataAttributes": {
        "page": "integer",  
        "type": "string",  
        "place": "string"
    }
}
```

### Output Examples:

#### Example 1 (Titles and Subtitles on Multiple Pages):
**Page 1:**
```json
{
    "metadataAttributes": {
        "page": 24,
        "type": "chapter",
        "place": "Descripción técnica de Synapse en la actualidad | Arquitectura Detallada de CNFs Regionales y Locales | CNF MEXICO KIO"
    }
}
```

**Page 2 (No new titles detected, carry over the existing titles):**
```json
{
    "metadataAttributes": {
        "page": 25,
        "type": "chapter",
        "place": [
            "Descripción técnica de Synapse en la actualidad | Arquitectura Detallada de CNFs Regionales y Locales | CNF MEXICO KIO"
        ]
    }
}
```

**Page 3 (No new titles detected, carry over the existing titles):**
```json
{
    "metadataAttributes": {
        "page": 26,
        "type": "chapter",
        "place":"Descripción técnica de Synapse en la actualidad | Arquitectura Detallada de CNFs Regionales y Locales | CNF MEXICO KIO"
    }
}
```

#### Example 2 (Section Spanning Multiple Pages):
**Page 1:**
```json
{
    "metadataAttributes": {
        "page": 86,
        "type": "chapter",
        "place": "Descripción técnica de Synapse en la actualidad | Arquitectura Detallada de CNFs Regionales y Locales | CNFs MADRID EQUINIX (Regional y Local BBVA España)"
        
    }
}
```

**Page 2 (No new titles, keep previous titles):**
```json
{
    "metadataAttributes": {
        "page": 87,
        "type": "chapter",
        "place": "Descripción técnica de Synapse en la actualidad | Arquitectura Detallada de CNFs Regionales y Locales | CNFs MADRID EQUINIX (Regional y Local BBVA España)"
    }
}
```

**Page 3 (New title detected):**
```json
{
    "metadataAttributes": {
        "page": 88,
        "type": "chapter",
        "place": "Descripción técnica de Synapse en la actualidad | Arquitectura Detallada de CNFs Regionales y Locales | CNFs MADRID EQUINIX (Regional y Local BBVA España) | Artículo 10 Leyes generales"
    }
}
```

**Important:** Return **only** the JSON for each image. Do not include any extra information, instructions, or commentary.