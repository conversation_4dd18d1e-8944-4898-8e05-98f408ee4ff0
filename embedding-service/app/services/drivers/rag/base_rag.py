from abc import ABC, abstractmethod


class BaseRAG(ABC):
    def __init__(self, kb_id: str = None):
        pass

    @abstractmethod
    def build_metadata_filter(filename: str):
        pass

    @abstractmethod
    def build_retrieval_configuration(
        self, number_of_results: int = 5, filename: str = None
    ):
        pass

    @abstractmethod
    async def execute(self, user_query: str = None, filename: str = None):
        pass
