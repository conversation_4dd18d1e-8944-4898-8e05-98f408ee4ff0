import boto3
from app.services.drivers.rag.base_rag import BaseRAG
import time


class RAGBedrock(BaseRAG):
    """
    Implementation of the BaseRAG interface for Amazon Bedrock Knowledge Base.

    This class provides methods to interact with Amazon Bedrock knowledge bases,
    enabling RAG (Retrieval Augmented Generation) queries with metadata filtering.

    Attributes:
        bedrock_agent_runtime: Boto3 client for bedrock-agent-runtime
        kb_id: Knowledge base identifier for queries
        debug_mode: Indicates if debug mode is enabled
    """

    def __init__(self, kb_id: str = None, region: str = "eu-west-1"):
        """
        Initializes the RAGBedrock client with AWS configuration and knowledge base settings.

        Args:
            kb_id (str, optional): Bedrock knowledge base identifier.
                If None, must be specified later.
            region (str, optional): AWS region to use. Defaults to "eu-west-1".
        """
        self.bedrock_agent_runtime = boto3.client(
            "bedrock-agent-runtime", region_name=region
        )
        self.kb_id = kb_id
        self.debug_mode = False
        self.region = region

    def set_debug(self, enabled: bool = True):
        """
        Enables or disables debug mode.

        When enabled, informational messages will be printed during RAG query execution,
        including configurations, filters, and results.

        Args:
            enabled (bool, optional): True to enable debug mode,
                False to disable it. Defaults to True.
        """
        self.debug_mode = enabled

    def build_metadata_filter(self, filename: str = None):
        """
        Constructs a metadata filter based on filename.

        This filter limits search results to documents with a specific filename.

        Args:
            filename (str, optional): Filename to filter results by.
                If None or "unknown", no filter will be applied.

        Returns:
            dict or None: Bedrock API metadata filter configuration,
                or None if no filter should be applied.
        """
        if not filename or filename == "unknown":
            print("No filename or 'unknown' filename provided, returning None for metadata filter")
            return None

        metadata_filter = {
            "equals": {
                "key": "filename",
                "value": filename,
            }
        }
        return metadata_filter

    def build_retrieval_configuration(
        self, number_of_results: int = 5, filename: str = None
    ):
        """
        Builds the complete retrieval query configuration.

        Combines the desired number of results with optional metadata filtering
        to create the vector search configuration.

        Args:
            number_of_results (int, optional): Maximum number of results to return.
                Defaults to 5.
            filename (str, optional): Filename to filter results by.
                If None or "unknown", no filter will be applied.

        Returns:
            dict: Complete Bedrock API retrieval configuration.
        """
        metadata_filter = self.build_metadata_filter(filename)
        vector_search_configuration = {
            "numberOfResults": number_of_results,
        }
        if metadata_filter:
            vector_search_configuration["filter"] = metadata_filter

        return {"vectorSearchConfiguration": vector_search_configuration}

    def _retrieve_internal(self, kb_id, retrieval_configuration, user_query):
        """
        Internal method to make the API call without retry logic.

        Makes a direct call to the Bedrock Agent Runtime service
        to execute the retrieval query.

        Args:
            kb_id (str): Knowledge base identifier.
            retrieval_configuration (dict): Retrieval configuration.
            user_query (str): User's query.

        Returns:
            dict: Raw Bedrock API response.
        """
        return self.bedrock_agent_runtime.retrieve(
            knowledgeBaseId=kb_id,
            retrievalConfiguration=retrieval_configuration,
            retrievalQuery={"text": user_query},
        )

    async def execute(
        self, user_query: str = None, filename: str = None, number_of_results: int = 5, filters: dict = None
    ):
        """
        Executes a RAG query with optional debug information.

        Handles the complete RAG query flow, including configuration building
        and direct query execution.

        Args:
            user_query (str): User's query text.
            filename (str, optional): Filename to filter results by.
                If None or "unknown", no filter will be applied.
            number_of_results (int, optional): Maximum number of results to return.
                Defaults to 5.

        Returns:
            dict: Bedrock RAG response containing retrieval results
                and associated metadata.

        Raises:
            Exception: If an error occurs during query execution or
                any other unexpected error.
        """
        if filters == {} or filters == None:
            rag_config = self.build_retrieval_configuration(
                number_of_results=number_of_results, filename=filename
            )
        else:
            rag_config = {"vectorSearchConfiguration": {"filter":filters, "numberOfResults":number_of_results}}
        try:
            rag_response = self._retrieve_internal(
                self.kb_id, rag_config, user_query
            )
        except Exception as e:
            print(f"Error in RAG execution: {type(e).__name__}: {str(e)}")
            raise e
        return rag_response
