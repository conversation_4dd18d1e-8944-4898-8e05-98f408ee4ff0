from typing import List, Optional, Union, <PERSON><PERSON>
from typing import List, Optional, Union, Tuple
from pgvector.asyncpg import register_vector
from app.services.drivers.embeddings.native.aws.embedding import AWSEmbedding


class RAGPostgres:
    def __init__(self, connector):
        self.connector = connector
        self.vector_encoder = AWSEmbedding(
            region="eu-west-1", model_id="amazon.titan-embed-text-v2:0"
        )

    async def query(
        self,
        query_text: str,
        top_k: int = 5,
        filename: Optional[str] = None,
        page_start: Optional[int] = None,
        page_end: Optional[int] = None,
        table_name: str = None,
    ):
        assert self.connector.pool is not None, "DBConnector not connected."

        query_embedding = await self.vector_encoder.get_embedding(query_text)
        
        async with self.connector.pool.acquire() as conn:
            await register_vector(conn)

            filters = []
            values = [query_embedding]
            param_index = 2  # $1 is the embedding

            if filename:
                filters.append(f"filename = ${param_index}")
                values.append(filename)
                param_index += 1

            # Only add page filters if they have meaningful values
            if page_start is not None and page_start > 0:
                filters.append(f"page >= ${param_index}")
                values.append(page_start)
                param_index += 1
            else:
                print(f"Skipping page_start filter: {page_start}")

            if page_end is not None and page_end > 0:
                filters.append(f"page <= ${param_index}")
                values.append(page_end)
                param_index += 1
            else:
                print(f"Skipping page_end filter: {page_end}")

            filter_clause = f"WHERE {' AND '.join(filters)}" if filters else ""

            sql = f"""
                SELECT *, 1 - (embedding <-> $1) AS distance
                FROM {table_name}
                {filter_clause}
                ORDER BY embedding <-> $1
                LIMIT {top_k}
            """

            rows = await conn.fetch(sql, *values)
            documents = [
                {
                    "chunks": row["chunks"],
                    "metadata": row["metadata"],
                    "page": row["page"],
                    "type": row["type"],
                    "place": row["place"],
                    "filename": row["filename"],
                    "distance": row["distance"],
                }
                for row in rows
            ]
        return documents
