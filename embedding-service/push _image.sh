#/bin/bash
docker build -t virtual-agent-embedding:1.1 .
docker tag virtual-agent-embedding:1.1 329599625202.dkr.ecr.eu-west-1.amazonaws.com/virtual-agent-embedding:1.1
aws ecr get-login-password --region eu-west-1 --profile AWSAdministratorAccess-329599625202 | docker login --username AWS --password-stdin 329599625202.dkr.ecr.eu-west-1.amazonaws.com
docker push 329599625202.dkr.ecr.eu-west-1.amazonaws.com/virtual-agent-embedding:1.1
