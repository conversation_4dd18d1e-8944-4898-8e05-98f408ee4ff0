

**Installing AWS CLI on Ubuntu**
=====================================

This guide provides step-by-step instructions on how to install the AWS CLI on Ubuntu. If you follow this steps, b<PERSON><PERSON> will be able to fetch your credentials, use your AWS account and make API calls to AWS services.

**Prerequisites**
---------------

* Ubuntu operating system
* `curl` and `unzip` packages installed (usually pre-installed on Ubuntu)
* `Access key ID` and `Secret access key` for your AWS account. You can obtain these following these steps: [AWS CLI Access key](https://docs.aws.amazon.com/IAM/latest/UserGuide/access-key-self-managed.html#Using_CreateAccessKey)

**Installation Steps**
--------------------

### Step 1: Download the AWS CLI installer

```bash
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
```

This command downloads the AWS CLI installer from the official AWS website.

### Step 2: Unzip the installer

```bash
unzip awscliv2.zip
```

This command extracts the contents of the zip file.

### Step 3: Run the installer

```bash
sudo ./aws/install
```

This command runs the installer with superuser privileges.

**Verify the Installation**
-------------------------

After completing the installation, verify that the AWS CLI is working correctly by running:

```bash
aws --version
```

This command should display the version of the AWS CLI installed on your system.

**Configuration**
-----------------

- Create a new AWS profile: `aws configure`.

> [!WARNING]
> Run this command only if **you are not** inside the project. Otherwise, the configuration file will be badly located.

If you need to run this command inside the project, use the following environment variables first:
- Export the AWS CLI configuration file: `export AWS_CONFIG_FILE=/home/<USER>/.aws/config`, replace `username` with your username.
- Export the AWS CLI credentials file: `export AWS_SHARED_CREDENTIALS_FILE=/home/<USER>/.aws/credentials`, replace `username` with your username.