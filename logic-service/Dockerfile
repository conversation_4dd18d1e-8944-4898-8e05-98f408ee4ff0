# Use an optimized Python base image
FROM python:3.12-slim-bookworm

# Update pip and setuptools
RUN pip install --no-cache-dir --upgrade pip setuptools

# Install Poetry
RUN pip install poetry

# Set working directory
WORKDIR /app

# Copy dependency files first to leverage Docker cache
COPY pyproject.toml poetry.lock ./

# Verify files were copied correctly (optional for debugging)
RUN ls -l /app

# Install dependencies before copying source code
RUN poetry install --no-root --no-interaction --no-ansi

# Now copy the rest of the code
COPY . .

# Expose application port
EXPOSE 5000

# Command to run the app
CMD ["poetry", "run", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
