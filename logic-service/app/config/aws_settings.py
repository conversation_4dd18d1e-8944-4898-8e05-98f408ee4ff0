from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict
import boto3
import os


def get_session():
    """
    Creates and returns a boto3 session with SSO credentials.
    Returns None if session creation fails.
    """
    try:
        print("STEP PASSED")
        session = boto3.Session()
        return session
    except Exception as e:
        print(f"Error creating AWS session: {e}")
        return None


class AWSSettings(BaseSettings):
    # AWS Configuration Settings
    kb_id: str = Field(alias="KB_ID")
    s3_bucket: str = Field(default="sourcedocumentationkb", alias="S3_BUCKET")
    aws_region: str = Field(default="eu-west-1", alias="AWS_REGION")

    model_config = {
        "env_file": ".env",  # Tells Pydantic where to find the .env file
        "env_file_encoding": "utf-8",
    }
