from pydantic import BaseModel
from typing import Optional, Literal
from app.services.utils.enums import AgentTypes


class UserQueryDTO(BaseModel):
    """
    Represents the user's query input.
    """

    user_query: str


class LLMConfigDTO(BaseModel):
    """
    Represents the configuration settings for Language Model requests.
    """

    model_id: str = "anthropic.claude-3-sonnet-20240229-v1:0"
    anthropic_version: Optional[str] = None
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None


class PostgresQueryConfig(BaseModel):
    page_start: int
    page_end: int
    table_name: str = "knowledge_base"


class RAGQueryDTO(BaseModel):
    """
    DTO that combines Retrieval and Generation functionality.
    This DTO allows performing complete RAG queries in a single call.

    Attributes:
        kb_id (str): Knowledge Base identifier
        top_k (Optional[int]): Number of documents to retrieve from the KB (default: 5)
        local_document_name (Optional[str]): Name of the local document to query
    """

    kb_id: str = "KGESVB3EJX"
    top_k: Optional[int] = 5
    local_document_name: Optional[str] = ""
    region: str = "eu-west-1"
    model_id: str = "amazon.titan-embed-text-v2:0"
    kb_type: str = "aws"
    query: str = ""
    postgres_config: Optional[PostgresQueryConfig]
    filters: dict = {}


class IngestQueryDTO(BaseModel):
    """
    DTO for document ingestion configuration.

    Attributes:
        parsing_strategy_LLM_configuration (LLMConfigDTO): LLM configuration for parsing strategy
        bucket_name (str): S3 bucket name for data ingestion (default: "data-ingestion-cecabank")
        folder_raw_documents (str): Folder for raw documents (default: "raw_documents")
        folder_parsed_documents (str): Folder for parsed documents (default: "parsed")
        parsing_strategy_prompt (str): Filename for parsing strategy prompt (default: "parsing_strategy.txt")
        page_init (int): Initial page for parsing (default: 1)
    """

    parsing_strategy_LLM_configuration: LLMConfigDTO
    bucket_name: str = "data-ingestion-cecabank"
    folder_raw_documents: str = "raw_documents"
    folder_parsed_documents: str = "parsed"
    parsing_strategy_prompt: str = "parsing_strategy.txt"
    page_init: int = 1


class ThreadDTO(BaseModel):
    """
    DTO for thread-based interactions with documents.

    Attributes:
        query (UserQueryDTO): The user's query
        document_name (str): Document name (example: 'resumen_ayala.pdf')
        agent_type (Literal["workflow", "agent"]): Type of agent to use (default: workflow)
        llm (LLMConfigDTO): LLM orchestration model configuration
        rag (RAGQueryDTO): RAG query configuration for document retrieval
    """

    query: UserQueryDTO
    agent_type: Literal["workflow", "agent"] = AgentTypes.AGENT_TYPE.value
    llm: LLMConfigDTO
    rag: RAGQueryDTO
    history: list = []
    guardrail_id: str = "66qbpt9jl1f7"
