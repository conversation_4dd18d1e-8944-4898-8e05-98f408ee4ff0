from fastapi import FastAPI
import logging

# from fastapi.middleware.cors import CORSMiddleware
from app.routes import *

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)


## Create app
app = FastAPI(
    title="Bedrock KB Test App",
    version="0.1.0",
    description="""This project is an AWS Bedrock Retrieval-Augmented Generation (RAG) system.
    It leverages the capabilities of AWS services to build a robust and scalable RAG solution.""",
)

## Add routers
app.include_router(health_controller)
app.include_router(chat_controller)
