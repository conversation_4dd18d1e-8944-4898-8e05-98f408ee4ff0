from fastapi import APIRouter
from app.config.aws_settings import AWSSettings
from app.dtos.dto import ThreadDTO
from app.services.drivers.llm.bedrock import BedrockLLM
from app.services.converse.workflow import workflow_pipeline
from app.services.converse.agentic import agentic_pipeline
from app.services.utils.enums import AgentTypes
from functools import lru_cache
from fastapi.responses import StreamingResponse
from botocore.config import Config


# Initialize FastAPI router for chat endpoints
router = APIRouter()


@lru_cache
def get_aws_settings():
    """
    Returns cached AWS settings to avoid repeated initialization.

    Returns:
        AWSSettings: The AWS configuration settings.
    """
    return AWSSettings()


@router.post("/chat/converse")
async def converse(thread: ThreadDTO):
    """
    Processes a conversation thread and generates a response using either a standard workflow
    or an agentic approach based on the specified agent type.

    This endpoint handles two different conversation processing approaches:
    1. Workflow: A structured, sequential processing pipeline with predefined steps
    2. Agent: An agentic approach with more autonomous decision-making capabilities

    Args:
        thread (ThreadDTO): The conversation thread data containing:
            - query (UserQueryDTO): The user's query with user_query field
            - agent_type (str): Type of agent to use ("workflow" or "agent")
            - llm (LLMConfigDTO): Configuration for the Language Model, including model_id
            - rag (RAGQueryDTO): Configuration for Retrieval Augmented Generation, including kb_id

    Returns:
        dict: A dictionary containing:
            - response (str): The generated response text
            - execution_time_ms (float): The execution time in milliseconds

    Raises:
        Exception: Any exceptions encountered during processing are propagated to the caller
                  and will be handled by FastAPI's exception handlers
    """
    try:
        llm = BedrockLLM(
            model_id=thread.llm.model_id,
            region="eu-west-1",
            config=Config(connect_timeout=60, read_timeout=600),
        )
        rag_config = thread.rag
        rag_config.query = thread.query.user_query
        if thread.agent_type == AgentTypes.WORKFLOW_TYPE.value:
            model_raw_response = await workflow_pipeline(
                messages=thread.history,
                llm=llm,
                rag_config=rag_config,
                user_query=thread.query.user_query,
            )
            return model_raw_response
        elif thread.agent_type == AgentTypes.AGENT_TYPE.value:
            streaming_generator = agentic_pipeline(
                messages=thread.history,
                rag_config=rag_config,
                llm=llm,
                user_query=thread.query.user_query,
                guardrail_id=thread.guardrail_id,
            )
            # Devolver la respuesta en streaming; se puede usar "text/event-stream" para SSE
            return StreamingResponse(
                streaming_generator, media_type="text/event-stream"
            )

        return "Not implemented"

    except Exception as e:
        # Propagate any exceptions to be handled by FastAPI's exception handlers
        raise e
