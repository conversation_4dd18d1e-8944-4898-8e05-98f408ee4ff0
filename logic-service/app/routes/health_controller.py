from fastapi import APIRouter
from app.services.health_service import HealthService


router = APIRouter()


@router.get("/health")
def health_check():
    """
    Endpoint to check the health status of the apllication

    Returns:
        HealthCheckResponse: An object containing the health status of the application.

    """
    health_service = HealthService()
    service_response = health_service.health_check()

    return service_response
