import json
from app.services.drivers.llm.llm_base import LLMBase
from app.dtos.dto import RAGQueryDTO
from app.services.utils.tools import (
    tools,
    process_tool_call,
)
from app.services.utils.prompts.agent_prompt import prompt_agent
from app.services.utils.helpers.chatbot_response import ChatbotResponse
from app.services.utils.enums import TaskTypes
from app.services.utils.helpers.aws.helper import build_tool_result_message

import json
import logging
from typing import AsyncGenerator

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

def extract_documents_from_rag(tool_result):
    """
    Extracts document filenames from RAG query results.
    
    Args:
        tool_result (dict): The result returned by a RAG tool call
        
    Returns:
        list: List of unique document filenames found in the results
    """
    documents = []
    
    # Check if tool_result contains retrieval results
    if not tool_result or "retrievalResults" not in tool_result:
        return documents
    
    # Extract filenames from each result's metadata
    for result in tool_result["retrievalResults"]:
        if "metadata" in result and "filename" in result["metadata"]:
            filename = result["metadata"]["filename"]
            # Only add unique filenames
            if filename and filename not in documents:
                documents.append(filename)
    
    return documents

async def chatbot_interaction(
    rag_config: RAGQueryDTO,
    user_message: str,
    chat_history: list = [],
    llm: LLMBase = None,
    guardrail_id: str = None,
) -> AsyncGenerator[dict, None]:
    """
    Asynchronous generator function that implements the chatbot interaction
    using an agentic approach. It yields various states/responses throughout the process.
    """
    logger.info(f"User Message: {user_message}")

    # Initialize conversation messages
    messages = [{"role": "user", "content": [{"text": user_message}]}]
    system_prompt = prompt_agent.format(history=str(chat_history))

    # First interaction: initial response
    try:
        yield ChatbotResponse.initial("I'm thinking...")
        response = await llm.converse(
            model_id=llm.model_id,
            max_tokens=4096,
            temperature=0,
            messages=messages,
            system_prompt=system_prompt,
            tools=tools,
            guardrail_id=guardrail_id,
        )
        yield {"type": "initial", "response": response}
        tool_or_stop = response.get("stopReason")
    except Exception as e:
        logger.exception("Error in initial conversation")
        yield ChatbotResponse.error(e)
        return

    logger.info("Initial Response:")
    logger.info(f"Stop Reason: {response.get('stopReason')}")
    logger.info(
        f"Content: {response.get('output', {}).get('message', {}).get('content')}"
    )

    documents_used = []

    # Loop to handle tool interactions
    while tool_or_stop == "tool_use":
        try:
            tool_use = next(
                block
                for block in response["output"]["message"]["content"]
                if isinstance(block, dict) and "toolUse" in block
            )
        except StopIteration:
            error_msg = "No 'toolUse' block found in the response content."
            logger.error(error_msg)
            yield ChatbotResponse.error(Exception(error_msg))
            return

        tool_name = tool_use["toolUse"]["name"]
        tool_input = tool_use["toolUse"]["input"]
        tool_use_id = tool_use["toolUse"]["toolUseId"]

        logger.info(
            f"Tool Used: {tool_name} - Tool Input: {json.dumps(tool_input, indent=2)}"
        )

        try:
            tool_result = await process_tool_call(
                tool_name=tool_name,
                tool_input=tool_input,
                rag_config=rag_config,
                llm=llm,
            )
            if tool_name in ["global_search", "local_search"]:
                docs_from_rag = extract_documents_from_rag(tool_result)
                documents_used.extend(docs_from_rag)
            yield ChatbotResponse.tool_result(tool_name, f"Using the tool: {tool_name}", documents_used)
        except Exception as e:
            logger.exception("Error processing tool call")
            yield ChatbotResponse.error(e)
            return

        logger.info(f"Tool Result: {tool_result}")
        # If the tool is of type SUMMARY and an LLM response is returned, finish the conversation
        if tool_name == TaskTypes.SUMMARY.value:
            if "llm_response" in tool_result:
                yield ChatbotResponse.final(tool_result["llm_response"])
                return

        # Build the tool result message and update the conversation messages
        message_tool_result = build_tool_result_message(
            response, tool_use_id, tool_result
        )
        messages.extend(message_tool_result)

        # Call the LLM in streaming mode for the next iteration
        try:
            response = await llm.converse(
                model_id=llm.model_id,
                max_tokens=4096,
                temperature=0,
                messages=messages,
                system_prompt=system_prompt,
                tools=tools,
                streaming=True,
                guardrail_id=guardrail_id,
            )
            # Process each chunk from the stream
            for chunk in response.get("stream", []):
                if "messageStop" in chunk:
                    tool_or_stop = chunk["messageStop"].get("stopReason")
                if "contentBlockDelta" in chunk:
                    delta_text = chunk["contentBlockDelta"]["delta"].get("text", "")
                    yield ChatbotResponse.thinking(delta_text)
        except Exception as e:
            logger.exception("Error in subsequent conversation")
            yield ChatbotResponse.error(e)
            return


async def agentic_pipeline(
    messages: list, rag_config: RAGQueryDTO, llm: LLMBase, user_query: str, guardrail_id: str
):
    async for update in chatbot_interaction(
        user_message=user_query, chat_history=messages, rag_config=rag_config, llm=llm, guardrail_id=guardrail_id
    ):
        yield json.dumps(update) + "\n"
