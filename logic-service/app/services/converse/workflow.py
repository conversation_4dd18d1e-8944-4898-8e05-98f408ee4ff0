from app.services.drivers.llm.llm_base import LLMBase
from app.services.utils.enums import TaskTypes
from app.services.drivers.embeddings.http_service import RAGService
from app.dtos.dto import RAGQueryDTO
import json


async def process_rag_query(
    llm: LLMBase = None,
    rag_config: dict = None,
    history: list = [],
):
    """
    Process a RAG query and format the response using the Language Model.

    Args:
        llm (LLMBase): Language Model service instance
        rag_config (dict): RAG configuration parameters
        history (list): Conversation history for context
    """
    # Execute RAG query - now with await since it's async
    rag_response = await RAGService().call(rag_config)

    # Prepare LLM query - Restore original format with "type" parameter
    messages = [
        {"role": "user", "content": [{"type": "text", "text": rag_config.query}]}
    ]
    system_prompt = """
    You are a helpful agent that answers user questions based on the provided RAG Context and chat history. Always cite the source document from the context in your answer. Do not use your own knowledge or create new documents; rely solely on the provided context.

# Instructions

1.  **Source Identification:** Identify the source document from the `"filename"` field within the `metadata` JSON object in the context.
2.  **Answering the Question:** Answer the user's question using only the information present in the `content.text` field of the context.
3.  **Citing the Source:** Always cite the source document in your answer using the following format: "Based on the document [filename], [answer to the question]."

# Output Format

Your response should be based in the user's question and cites the source document.

# Examples

**RAG Context: **


```json
{
    "content":{
        "text":"El presidente del Real Madrid ha contratado a Figo como nuevo refuerzo del club merengue...",
        "type":"TEXT"
    },
    "location":{
        "s3Location":{
            "uri":"real_madrid_news.pdf"
        },
        "type":"S3"
    },
    "metadata":{
        "filename":"real_madrid_news.pdf",
        "page":21.0,
        "place":"Resumen de El Quijote, por Francisco Ayala",
        "type":"chapter"
    },
    "score":0.46050587
}
```

**User history chat:** (This field is used to provide context to the agent, but is not used in this example)

**User Query:** Who signed Figo?

**Expected Response:** Basado en el documento real_madrid_news.pdf, Figo es el nuevo fichaje del Real Madrid gracias al presidente.

# Notes

*   Pay close attention to the `"filename"` field for the document source.
*   Answer the question concisely, using only the information from the `content.text` field.
*   Always provide the answer in the format "Based on the document [filename], [answer to the question]."
*   If the filename contains a path, only use the filename itself (e.g., if the URI is `s3://bucket/path/to/document.pdf`, use `document.pdf`).
*   The response should be in the same language as the context.

# Important

*  If the RAG is empty confirm with the user the filename if it is necessary.

    """
    rag_and_history = f"RAG Context: {rag_response} User history chat: {history}"
    system_prompt = system_prompt + rag_and_history

    # Get LLM response
    response = await llm.invoke_model(system_prompt, messages)

    return response


async def _orchestrator_internal(llm: LLMBase = None, messages: list = []):
    """
    Internal orchestrator function to process user input and determine appropriate actions.

    Args:
        llm (LLMBase): Language Model service instance
        messages (list): List of conversation messages

    Returns:
        str: JSON response containing action classification and extracted information
    """
    system_prompt = """
Classify user input into one of the following actions: "global", "local", or "summary". Determine if a file is referenced (True/False) and extract the filename if applicable. Use the chat history to inform the `user_question` field.

# Rules

1.  **"global"**:
    *   The user does not specify a document.
    *   The user mentions a document without a clear extension.

2.  **"local"**:
    *   The user has confirmed a document from the history in recent messages.
    *   The user provides a specific document with its extension.
    *   Only use this action if a document has been explicitly confirmed or provided with its extension.

3.  **"summary"**:
    *   The user explicitly requests a summary.

# Output Format

Output a JSON object with the following keys:

*   `action` (string): One of "global", "local", or "summary".
*   `file` (boolean): True if a specific file is referenced, False otherwise.
*   `filename` (string, optional): The filename, including the extension if provided by the user. Only include if `file` is True or the filename is explicitly mentioned in the user's request.
*   `user_question` (string): The user's main question, incorporating relevant chat history for context.

# Examples

```json
{
    "action":"global",
    "file": False,
    "user_question": "What is the best performance made in super bowl?"
}
```

```json
{
    "action":"global",
    "file": False,
    "user_question": "What is the best performance made in super bowl based in document Super bowl?"
}
```

```json
{
    "action": "local",
    "file": True,
    "filename": "Prision_Break.pdf",
    "user_question": "I would like to know what Prision_Break.pdf book says about Ragnar"
}
```

(The following example is part of a multi-turn conversation. The `user_question` should reflect the original question, not the follow-up.)

```json
{
    "action": "local",
    "file": True,
    "filename": "X99.pdf",
    "user_question": "what are microservices?"
}
```

(The following example is part of a multi-turn conversation. The `user_question` should reflect the original question, not the follow-up.)

```json
{
    "action": "local",
    "file": True,
    "filename": "Y88.pdf",
    "user_question": "what are microservices?"
}
```

```json
{
    "action": "summary",
    "file": True,
    "user_question": "i want a summary of stranger things document."
}
```

```json
{
    "action": "summary",
    "file": True,
    "filename": "[file name from the last conversation]",
    "user_question": "i want a summary of that document."
}
```

# Notes

*   Always return a well-formatted JSON response.
*   Do not add file extensions unless the user explicitly provides them.
*   If the user does not specify a file extension or confirm a file in the chat, the action should be "global".
*   The `user_question` field should always reflect the original question, even in multi-turn conversations.
*   If the user refers to "that document" or similar, infer the filename from the previous turn in the conversation.
*   Do not add any string or explanation, just return a JSON response. Do not add special characters.
"""
    response = await llm.invoke_model(system_prompt, messages)
    return response


async def orchestrator(llm: LLMBase = None, messages: list = []):
    """
    Main orchestrator function that handles message classification and routing.
    Wraps the internal orchestrator with error handling.

    Args:
        llm (LLMBase): Language Model service instance
        messages (list): List of conversation messages

    Returns:
        dict: Parsed JSON response containing action classification

    Raises:
        Exception: If orchestration fails
    """
    try:
        response = await _orchestrator_internal(llm=llm, messages=messages)
        print(f"Orchestration: {response}")
        response = json.loads(response)
        return response
    except Exception as e:
        print(f"Error in orchestration: {type(e).__name__}: {str(e)}")
        raise e


async def workflow_pipeline(messages, rag_config: RAGQueryDTO, llm: LLMBase):
    # Get action classification from orchestrator
    tool = await orchestrator(llm, messages)
    # Extract action parameters
    action = tool["action"]
    filename = tool["filename"] if "filename" in tool else None
    user_query = tool["user_question"]
    is_file = tool["file"]
    # Process request based on determined action
    if action == TaskTypes.GLOBAL_SEARCH.value:
        print(f"Using {action}")
        response = await process_rag_query(
            llm=llm, rag_config=rag_config, user_query=user_query
        )
    elif action == TaskTypes.LOCAL_SEARCH.value and is_file:
        print(f"Using {action}")
        rag_config.local_document_name = filename
        response = await process_rag_query(
            llm=llm, rag_config=rag_config, user_query=user_query
        )
    elif action == TaskTypes.SUMMARY.value:
        ...

    print(response)
    return response
