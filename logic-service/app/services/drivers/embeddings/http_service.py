import httpx
import os
import json
import logging

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class RAGService:
    def __init__(self, base_url: str = None):
        # Retrieve base_url from environment variable or use default
        self.base_url = base_url or os.getenv(
            "EMBEDDING_BASE_URL", "http://localhost:8001"
        )
        self.endpoint = "/rag"
        logger.info(f"RAGService initialized with base_url: {self.base_url}")

    async def call(self, payload: dict) -> dict:
        async with httpx.AsyncClient() as client:
            logger.info(f"Calling embedding service at {self.base_url}{self.endpoint}")
            logger.info(f"Payload being sent: {json.dumps(payload, indent=2)}")

            try:
                response = await client.post(
                    f"{self.base_url}{self.endpoint}",
                    json=payload,
                    timeout=30.0
                )

                logger.info(f"Response status code: {response.status_code}")

                if response.status_code != 200:
                    logger.error(f"HTTP Error {response.status_code}: {response.text}")
                    raise httpx.HTTPStatusError(
                        f"HTTP {response.status_code}",
                        request=response.request,
                        response=response
                    )

                response_data = response.json()
                logger.info(f"Response received successfully. Keys: {list(response_data.keys()) if isinstance(response_data, dict) else 'Not a dict'}")

                return response_data

            except httpx.TimeoutException as e:
                logger.error(f"Timeout calling embedding service: {e}")
                raise
            except httpx.HTTPStatusError as e:
                logger.error(f"HTTP error calling embedding service: {e}")
                raise
            except Exception as e:
                logger.error(f"Unexpected error calling embedding service: {type(e).__name__}: {e}")
                raise


class EmbeddingService:

    def __init__(self, base_url: str):
        self.base_url = base_url
        self.endpoint = "/embedding"

    async def query(self, payload: dict) -> dict:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}{self.endpoint}", json=payload
            )
            return response.json()
