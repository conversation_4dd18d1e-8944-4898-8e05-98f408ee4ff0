import httpx
import os


class RAGService:
    def __init__(self, base_url: str = None):
        # Retrieve base_url from environment variable or use default
        self.base_url = base_url or os.getenv(
            "EMBEDDING_BASE_URL", "http://embedding-service:8001"
        )
        self.endpoint = "/rag"

    async def call(self, payload: dict) -> dict:
        async with httpx.AsyncClient() as client:
            print("Calling embedding service")
            response = await client.post(
                f"{self.base_url}{self.endpoint}", json=payload
            )
            return response.json()


class EmbeddingService:

    def __init__(self, base_url: str):
        self.base_url = base_url
        self.endpoint = "/embedding"

    async def query(self, payload: dict) -> dict:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}{self.endpoint}", json=payload
            )
            return response.json()
