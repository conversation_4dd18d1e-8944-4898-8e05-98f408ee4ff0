import json
import boto3
from botocore.exceptions import ClientError
from botocore.config import Config
from app.services.drivers.llm.llm_base import LLMBase
from typing import Dict, Any, List


class BedrockLLM(LLMBase):
    """
    Implementation of the LLMBase interface for Amazon Bedrock LLM service.

    This class provides methods to interact with Amazon Bedrock's Language Models,
    particularly optimized for Anthropic Claude models.

    Attributes:
        client: The boto3 client for bedrock-runtime
        model_id: The default model identifier for requests
        region: AWS region for the Bedrock service
    """

    def __init__(
        self,
        model_id: str = "anthropic.claude-3-5-sonnet-20240620-v1:0",
        region: str = "eu-west-1",
        config: Config = Config(connect_timeout=60, read_timeout=60)

    ):
        """
        Initialize the BedrockLLM with AWS client and model configuration.

        Args:
            model_id (str): The default Bedrock model ID to use.
                Defaults to Claude 3.5 Sonnet.
            region (str): AWS region for the Bedrock service.
                Defaults to "eu-west-1".
        """
        
        self.client = boto3.client("bedrock-runtime", region_name=region, config=config)
        self.model_id = model_id
        self.region = region
        self.config = config

    async def invoke_model(
        self,
        model_id: str = None,
        max_tokens: int = 1000,
        temperature: float = 0.0,
        streaming=False,
        messages=[],
        system_prompt: str = "Answer base in the user history",
        anthropic_version: str = "bedrock-2023-05-31",
    ) -> Dict[str, Any]:
        """
        Internal method to invoke the Bedrock model.

        This method formats the request according to Anthropic's API specifications
        and calls the appropriate Bedrock API endpoint based on streaming preference.

        Note: boto3 calls are synchronous, but this method is async for interface consistency.

        Args:
            model_id (str, optional): The model ID to use. Defaults to self.model_id.
            max_tokens (int, optional): Maximum number of tokens in the response. Defaults to 1000.
            temperature (float, optional): Sampling temperature for generation. Defaults to 0.0.
            streaming (bool, optional): Whether to use streaming response. Defaults to False.
            messages (list, optional): List of message objects in the format
                [{"role": "user", "content": [{"type": "text", "text": "message"}]}]
            system_prompt (str, optional): System prompt to guide model behavior.
                Defaults to "Answer base in the user history".

        Returns:
            Dict[str, Any]: The raw response from the Bedrock API
        """
        native_request = {
            "anthropic_version": anthropic_version,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "messages": messages,
            "system": system_prompt,
        }

        request_json = json.dumps(native_request)

        if streaming:
            response = self.client.invoke_model_with_response_stream(
                modelId=model_id or self.model_id, body=request_json
            )
        else:
            response = self.client.invoke_model(
                modelId=model_id or self.model_id, body=request_json
            )
        return json.loads(response["body"].read())["content"][0]["text"]

    async def converse(
        self,
        model_id: str = None,
        messages: List[Dict[str, Any]] = None,
        system_prompt: str = "Answer based on the user history",
        tools: List[Dict[str, Any]] = None,
        temperature: float = 0.0,
        max_tokens: int = 1000,
        streaming: bool = False,
        guardrail_id: str = None,
    ) -> Dict[str, Any]:
        # Inicializar messages y tools como listas vacías si son None
        messages = messages or []
        tools = tools or []

        # Construir el diccionario de parámetros comunes
        params = {
            "modelId": model_id,
            "inferenceConfig": {
                "maxTokens": max_tokens,
                "temperature": temperature,
            },
            "messages": messages,
            "system": [{"text": system_prompt}],
        }

        # Añadir toolConfig solo si tools no está vacío
        if tools:
            params["toolConfig"] = {"tools": tools}

        if guardrail_id:
            params["guardrailConfig"] = {
                "guardrailIdentifier": guardrail_id,
                "guardrailVersion": "DRAFT"  # o usa "LATEST" para la versión publicada
        }    

        # Llamar al método correspondiente según el valor de streaming
        if streaming:
            response = self.client.converse_stream(**params)
        else:
            response = self.client.converse(**params)

        return response
