from typing import Dict, Any

from abc import ABC, abstractmethod


class LLMBase(ABC):
    """
    Abstract interface for an LLM driver that handles different providers.
    """

    def init(self): ...

    @abstractmethod
    async def invoke_model(
        self,
        model_id: str,
        max_tokens: int = 1000,
        temperature: float = 0.0,
        streaming=False,
        messages=[],
        system_prompt: str = "",
    ) -> Dict[str, Any]:
        """
        Invoke the LLM model with the provided prompt.

        :param model_id: ID of the model to invoke.
        :param prompt: Input text for the model.
        :param max_tokens: Maximum number of tokens in the response.
        :param temperature: Temperature parameter for generation.
        :return: Model response.
        :raises ClientError: If there is an error invoking the model.
        :raises Exception: For other unexpected errors.
        """
        pass

    @abstractmethod
    async def converse(
        self,
        model_id: str,
        max_tokens: int = 1000,
        temperature: float = 0.0,
        streaming=False,
        messages=[],
        system_prompt: str = "",
        guardrail_id: str = None,
    ) -> Dict[str, Any]:
        """
        Invoke the LLM model with the provided prompt.

        :param model_id: ID of the model to invoke.
        :param prompt: Input text for the model.
        :param max_tokens: Maximum number of tokens in the response.
        :param temperature: Temperature parameter for generation.
        :return: Model response.
        :raises ClientError: If there is an error invoking the model.
        :raises Exception: For other unexpected errors.
        """
        pass
