from enum import Enum


class AgentTypes(Enum):
    """
    Defines the types of agents available in the system.

    Attributes:
        WORKFLOW_TYPE: Represents a structured, sequential processing pipeline.
        AGENT_TYPE: Represents an agentic approach with more autonomous decision-making capabilities.
    """

    WORKFLOW_TYPE = "workflow"
    AGENT_TYPE = "agent"


class TaskTypes(Enum):
    """
    Defines the types of tasks that can be performed by the system.

    Attributes:
        SUMMARY: Task to generate a summary of content.
        LOCAL_SEARCH: Task to perform a search within local context.
        GLOBAL_SEARCH: Task to perform a broader search across all available content.
    """

    SUMMARY = "get_summary"
    LOCAL_SEARCH = "local_search"
    GLOBAL_SEARCH = "global_search"
