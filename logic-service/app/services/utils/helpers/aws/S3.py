import boto3
from typing import Optional, Union
import logging

# Initialize the S3 client 
s3_client = boto3.client("s3")

# Setup logging
logger = logging.getLogger(__name__)

def upload_s3(bucket_name: str, file: bytes, filename: str, content_type: str = "text/plain") -> bool:
    """
    Uploads a file to an S3 bucket.
    
    Args:
        bucket_name (str): Name of the S3 bucket to upload to
        file (bytes): File content as bytes to upload
        filename (str): The key (path + filename) to use in S3
        content_type (str, optional): Content type of the file. Defaults to "text/plain".
        
    Returns:
        bool: True if upload was successful, False otherwise
    """
    try:
        if not bucket_name:
            logger.warning("Cannot upload: bucket name not provided")
            return False
            
        s3_client.put_object(
            Bucket=bucket_name,
            Key=filename,
            Body=file,
            ContentType=content_type,
        )
        logger.info(f"Successfully uploaded to s3://{bucket_name}/{filename}")
        return True
    except Exception as e:
        logger.error(f"Error uploading to S3: {str(e)}")
        return False

def download_s3(bucket_name: str, key: str) -> Optional[str]:
    """
    Downloads a file from an S3 bucket and returns its contents.
    
    Args:
        bucket_name (str): Name of the S3 bucket to download from
        key (str): The key (path + filename) of the file in S3
        
    Returns:
        Optional[str]: File content as string if successful, None otherwise
    """
    try:
        # First check if the file exists
        s3_client.head_object(Bucket=bucket_name, Key=key)
        
        # If we get here, the file exists - download it
        response = s3_client.get_object(Bucket=bucket_name, Key=key)
        content = response["Body"].read().decode("utf-8")
        logger.info(f"Successfully downloaded s3://{bucket_name}/{key}")
        return content
    except Exception as e:
        logger.error(f"Error downloading from S3: {str(e)}")
        return None
