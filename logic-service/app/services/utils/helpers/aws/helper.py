def build_tool_result_message(
    response: dict, tool_use_id: str, tool_result: dict
) -> list:
    """
    Builds a message block to continue a conversation in Amazon Bedrock (Claude)
    after a tool use.

    This format is specific to Amazon Bedrock's `converse()` API, where the model
    returns a `toolUse` block and expects a `toolResult` message in response,
    associated with the corresponding `toolUseId`.

    Args:
        response (dict): The previous model response, containing the toolUse block.
        tool_use_id (str): The unique identifier of the tool use block.
        tool_result (dict): The actual result computed by your tool.

    Returns:
        list: A list of two messages — one from the assistant (previous response)
              and one from the user, containing the tool result linked to the
              original toolUseId. This is required by <PERSON>'s tool calling flow.
    """
    return [
        {"role": "assistant", "content": response["output"]["message"]["content"]},
        {
            "role": "user",
            "content": [
                {
                    "toolResult": {
                        "toolUseId": tool_use_id,
                        "content": [{"json": {"result": tool_result}}],
                    }
                }
            ],
        },
    ]
