class ChatbotResponse:
    @staticmethod
    def initial(text: str):
        return {"type": "initial", "response": text}

    @staticmethod
    def tool_result(name: str, text: str, documents_used: list = None):
        if documents_used is None:
            documents_used = []
        return {"type": "tool_result", "tool_name": name, "response": text, "documents_used": documents_used}

    @staticmethod
    def thinking(text: str):
        return {"type": "thinking", "response": text}

    @staticmethod
    def final(response: str):
        return {"type": "final", "response": response}

    @staticmethod
    def error(e: Exception):
        return {"type": "error", "error": f"{type(e).__name__}: {str(e)}"}