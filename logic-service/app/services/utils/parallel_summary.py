import asyncio
from typing import List, Dict, Any
import math
import json
import aiofiles
import random
from botocore.config import Config
from app.dtos.dto import RAGQueryDTO
from app.services.drivers.embeddings.http_service import RAGService
from app.services.utils.prompts.summary_prompt import prompt_summary
from app.services.drivers.llm.llm_base import LLMBase

# Semáforo para controlar el número máximo de llamadas concurrentes a la API
MAX_CONCURRENT_CALLS = 10


async def make_async_boto3_call(func, *args, **kwargs):
    """
    Convierte una llamada síncrona de boto3 en asíncrona.
    """
    return await asyncio.to_thread(func, *args, **kwargs)


def extraer_texto_concatenado(datos_json):
    """
    Extrae y concatena el texto de 'retrievalResults' en el JSON proporcionado, ordenado por el número de página.

    :param datos_json: Diccionario que contiene la estructura JSON.
    :return: Cadena con todo el texto concatenado y ordenado por página.
    """
    # Extraer la lista de resultados de recuperación
    resultados = datos_json.get("retrievalResults", [])

    # Ordenar los resultados por el número de página en los metadatos
    resultados_ordenados = sorted(
        resultados, key=lambda x: x.get("metadata", {}).get("page", float("inf"))
    )

    # Extraer y concatenar el texto de cada resultado ordenado
    texto_concatenado = " ".join(
        resultado.get("content", {}).get("text", "")
        for resultado in resultados_ordenados
    )

    return texto_concatenado


async def process_chunk_with_llm(
    rag_config: RAGQueryDTO = None,
    start_page: int = None,
    end_page: int = None,
    semaphore: asyncio.Semaphore = None,
    llm: LLMBase = None,
) -> Dict[str, Any]:
    """
    Process a chunk of pages from a document using RAG and LLM.

    Args:
        bedrock_client: Bedrock Runtime client for LLM
        agent_runtime: Bedrock Agent Runtime client for RAG
        knowledge_base_id (str): Knowledge base ID
        filename (str): Name of the file to process
        start_page (int): Start page number
        end_page (int): End page number
        chunk_size (int): Maximum number of pages per chunk
        model_id (str): LLM model ID. Defaults to Claude 3 Sonnet.
        semaphore (asyncio.Semaphore): Semaphore for controlling concurrent API calls

    Returns:
        Dict[str, Any]: The processed chunk with LLM response
    """
    # First get RAG results
    metadata_filter = {
        "andAll": [
            {"equals": {"key": "filename", "value": rag_config.local_document_name}},
            {"greaterThanOrEquals": {"key": "page", "value": float(start_page)}},
            {"lessThanOrEquals": {"key": "page", "value": float(end_page)}},
        ]
    }

    # Use a detailed query that requests comprehensive information
    detailed_query = (
        "Proporciona un resumen EXHAUSTIVO y DETALLADO de esta sección del documento legal. "
        "REQUISITOS OBLIGATORIOS:\n"
        "1. Incluir ABSOLUTAMENTE TODOS los puntos clave, artículos, disposiciones y medidas.\n"
        "2. NO omitir NINGUNA información relevante.\n"
        "3. Provide a comprehensive summary. You may use up to the maximum available length to ensure nothing is missed.\n"
        "4. Estructurar la información de manera clara y organizada.\n"
        "5. Incluir referencias específicas a artículos y secciones.\n"
        "6. NO condensar ni resumir excesivamente - se requiere el máximo nivel de detalle.\n"
        "7. Preservar todas las cifras, fechas y datos específicos mencionados.\n"
        "IMPORTANTE: Este es un documento legal donde cada detalle es crucial. La respuesta debe ser extensa y completa."
    )

    # Calculate the exact number of results we need based on the requested page range
    actual_pages_requested = end_page - start_page + 1

    print(f"number of results: {actual_pages_requested}")
    try:
        rag_config.filters = metadata_filter
        rag_config.top_k = actual_pages_requested
        rag_config.query = detailed_query
        if semaphore:
            async with semaphore:
                rag_response = await RAGService().call(rag_config.dict())
        else:
            rag_response = await RAGService().call(rag_config.dict())
        extracted_text = extraer_texto_concatenado(rag_response)
        system_prompt = prompt_summary
        message = [
            {
                "role": "user",
                "content": [
                    {
                        "text": f"Resume el siguiente texto de manera fiel y completa, sin añadir contenido externo: {extracted_text}"
                    }
                ],
            }
        ]
        if semaphore:
            async with semaphore:
                llm_response = await llm.converse(
                    model_id=llm.model_id,
                    temperature=0.3,
                    max_tokens=4096,
                    messages=message,
                    system_prompt=system_prompt,
                )
        else:
            llm_response = await llm.converse(
                model_id=llm.model_id,
                temperature=0.3,
                max_tokens=4096,
                messages=message,
                system_prompt=system_prompt,
            )

        llm_text = next(
            (
                block["text"]
                for block in llm_response["output"]["message"]["content"]
                if "text" in block
            ),
            None,
        )
        # Return both RAG and LLM results
        return {
            "rag_results": rag_response,
            "llm_response": llm_text,
            "page_range": {"start": start_page, "end": end_page},
        }

    except Exception as e:
        print(
            f"Error processing chunk {start_page}-{end_page}: {type(e).__name__}: {str(e)}"
        )
        raise e


async def parallel_summary(
    rag_config: RAGQueryDTO = None,
    total_chunks: int = 0,
    chunk_size: int = 5,
    start_page: int = None,
    end_page: int = None,
    llm=None,
) -> List[Dict[str, Any]]:
    """
    Process document chunks in parallel using asyncio, including LLM processing.

    Args:
        bedrock_client: Bedrock Runtime client for LLM
        agent_runtime: Bedrock Agent Runtime client for RAG
        knowledge_base_id (str): Knowledge base ID
        filename (str): Name of the file to process
        total_chunks (int): Total number of chunks to process
        chunk_size (int): Maximum number of pages per chunk
        start_page (int, optional): Start page number
        end_page (int, optional): End page number
        model_id (str): LLM model ID

    Returns:
        List[Dict[str, Any]]: List of responses from all chunks
    """
    if start_page is None:
        start_page = 1

    if end_page is None:
        end_page = start_page + (total_chunks * chunk_size) - 1

    # Calculate chunk boundaries
    total_pages = end_page - start_page + 1
    num_chunks = math.ceil(total_pages / chunk_size)

    # Create semaphore to limit concurrent API calls
    semaphore = asyncio.Semaphore(MAX_CONCURRENT_CALLS)

    # Create tasks for each chunk with semaphore
    tasks = []
    for i in range(num_chunks):
        chunk_start = start_page + (i * chunk_size)
        chunk_end = min(chunk_start + chunk_size - 1, end_page)
        print(chunk_start, "to", chunk_end)
        task = process_chunk_with_llm(
            rag_config, chunk_start, chunk_end, semaphore, llm
        )
        tasks.append(task)

    # Execute all tasks in parallel with controlled concurrency
    results = await asyncio.gather(*tasks)
    return results


def combine_chunk_results(results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Combine results from multiple chunks into a single response.

    Args:
        results (List[Dict[str, Any]]): List of chunk results

    Returns:
        Dict[str, Any]: Combined response with both RAG and LLM results
    """
    # Sort chunks by page range to maintain document order
    sorted_results = sorted(results, key=lambda x: x["page_range"]["start"])

    # Combine RAG results
    combined_rag_results = []
    for chunk in sorted_results:
        if "rag_results" in chunk and "retrievalResults" in chunk["rag_results"]:
            combined_rag_results.extend(chunk["rag_results"]["retrievalResults"])

    # Combine LLM responses
    combined_llm_response = "\n\n".join(
        f"[Páginas {chunk['page_range']['start']}-{chunk['page_range']['end']}]:\n{chunk['llm_response']}"
        for chunk in sorted_results
        if chunk.get("llm_response")
    )

    return {
        "retrievalResults": combined_rag_results,
        "llm_response": combined_llm_response,
    }
