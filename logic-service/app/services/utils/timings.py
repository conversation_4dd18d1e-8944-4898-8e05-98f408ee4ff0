import time
import statistics
from typing import Dict, Any, Callable, Awaitable, <PERSON><PERSON>
import threading

# Dictionary to store timing results for different pipeline types
timing_results = {"workflow": [], "agent": []}

# Dictionary to track throttling wait times for each thread
throttling_wait_times = {}
throttling_lock = threading.Lock()


def reset_timings():
    """
    Reset all timing data for both workflow and agent pipelines.
    """
    global throttling_wait_times
    timing_results["workflow"] = []
    timing_results["agent"] = []
    throttling_wait_times = {}


def record_throttling_wait(delay_seconds: float):
    """
    Record time spent waiting due to throttling.

    Args:
        delay_seconds: The delay time in seconds
    """
    thread_id = threading.get_ident()
    with throttling_lock:
        if thread_id not in throttling_wait_times:
            throttling_wait_times[thread_id] = 0
        throttling_wait_times[thread_id] += delay_seconds


def get_and_reset_throttling_wait():
    """
    Get the total throttling wait time for the current thread and reset it.

    Returns:
        float: The total throttling wait time in seconds
    """
    thread_id = threading.get_ident()
    with throttling_lock:
        wait_time = throttling_wait_times.get(thread_id, 0)
        throttling_wait_times[thread_id] = 0
    return wait_time


def get_timing_stats(agent_type: str = None) -> Dict[str, Any]:
    """
    Get statistics for the timing results.

    Args:
        agent_type: Optional filter for agent type. If None, returns stats for all types.

    Returns:
        Dictionary with timing statistics including count, average, min, max, median,
        standard deviation (if applicable), and total execution time.
    """
    stats = {}

    if agent_type:
        # Return stats for specific agent type
        times = timing_results.get(agent_type, [])
        if times:
            stats[agent_type] = {
                "count": len(times),
                "avg_ms": statistics.mean(times),
                "min_ms": min(times),
                "max_ms": max(times),
                "median_ms": statistics.median(times),
                "total_ms": sum(times),
            }
            if len(times) > 1:
                stats[agent_type]["stdev_ms"] = statistics.stdev(times)
    else:
        # Return stats for all agent types
        for agent_type, times in timing_results.items():
            if times:
                stats[agent_type] = {
                    "count": len(times),
                    "avg_ms": statistics.mean(times),
                    "min_ms": min(times),
                    "max_ms": max(times),
                    "median_ms": statistics.median(times),
                    "total_ms": sum(times),
                }
                if len(times) > 1:
                    stats[agent_type]["stdev_ms"] = statistics.stdev(times)

    return stats


def print_timing_comparison():
    """
    Print a comparison of timing results between agent types.

    This function displays a formatted comparison of performance metrics
    between workflow and agent pipelines, including average, median, min, max,
    standard deviation, and total execution times. If both pipeline types
    have timing data, it also calculates and displays which one is faster.
    """
    stats = get_timing_stats()

    if not stats:
        print("No timing data available.")
        return

    print("\n" + "=" * 60)
    print("TIMING COMPARISON")
    print("=" * 60)

    for agent_type, agent_stats in stats.items():
        print(f"\n{agent_type.upper()} PIPELINE:")
        print(f"  Requests:     {agent_stats['count']}")
        print(f"  Average:      {agent_stats['avg_ms']:.2f} ms")
        print(f"  Median:       {agent_stats['median_ms']:.2f} ms")
        print(f"  Min:          {agent_stats['min_ms']:.2f} ms")
        print(f"  Max:          {agent_stats['max_ms']:.2f} ms")
        if "stdev_ms" in agent_stats:
            print(f"  Std Dev:      {agent_stats['stdev_ms']:.2f} ms")
        print(f"  Total time:   {agent_stats['total_ms']:.2f} ms")

    # If we have both workflow and agent stats, show comparison
    if "workflow" in stats and "agent" in stats:
        workflow_avg = stats["workflow"]["avg_ms"]
        agent_avg = stats["agent"]["avg_ms"]

        if workflow_avg > agent_avg:
            diff_pct = ((workflow_avg - agent_avg) / workflow_avg) * 100
            faster = "agent"
            slower = "workflow"
        else:
            diff_pct = ((agent_avg - workflow_avg) / agent_avg) * 100
            faster = "workflow"
            slower = "agent"

        print("\n" + "-" * 60)
        print(
            f"RESULT: {faster.upper()} is {diff_pct:.2f}% faster than {slower.upper()}"
        )
        print("-" * 60 + "\n")


async def time_function(
    func: Callable[..., Awaitable], agent_type: str, *args, **kwargs
) -> Tuple[Any, float]:
    """
    Measure the execution time of an async function.

    This function wraps an async function call with timing logic, recording the
    execution time and storing it in the timing_results dictionary for the
    specified agent type. It subtracts any time spent waiting due to throttling.

    Args:
        func: The async function to time
        agent_type: The type of agent ("workflow" or "agent")
        *args, **kwargs: Arguments to pass to the function

    Returns:
        Tuple of (function result, execution time in milliseconds)
    """
    # Reset throttling wait time for this thread
    get_and_reset_throttling_wait()

    start_time = time.time()
    result = await func(*args, **kwargs)
    end_time = time.time()

    # Get throttling wait time and subtract it from execution time
    throttling_wait_seconds = get_and_reset_throttling_wait()

    # Calculate execution time in milliseconds, excluding throttling wait time
    raw_execution_time_ms = (end_time - start_time) * 1000
    adjusted_execution_time_ms = raw_execution_time_ms - (
        throttling_wait_seconds * 1000
    )

    # Ensure we don't have negative time (in case of timing issues)
    adjusted_execution_time_ms = max(0, adjusted_execution_time_ms)

    timing_results[agent_type].append(adjusted_execution_time_ms)

    if throttling_wait_seconds > 0:
        print(
            f"\n[TIMING] {agent_type.upper()} execution time: {adjusted_execution_time_ms:.2f} ms "
            f"(excluded {throttling_wait_seconds:.2f}s of throttling wait)"
        )
    else:
        print(
            f"\n[TIMING] {agent_type.upper()} execution time: {adjusted_execution_time_ms:.2f} ms"
        )

    return result, adjusted_execution_time_ms
