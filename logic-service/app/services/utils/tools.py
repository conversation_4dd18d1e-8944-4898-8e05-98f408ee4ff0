from app.services.drivers.embeddings.http_service import RAGService
from app.dtos.dto import RAGQueryDTO
import boto3
import math
from .parallel_summary import (
    parallel_summary,
    combine_chunk_results,
    process_chunk_with_llm,
)
from app.services.drivers.embeddings.http_service import RAGService
from app.services.utils.enums import TaskTypes
from app.services.utils.helpers.aws.S3 import download_s3

# Default configuration
MAX_PAGE_RANGE = 50  # Maximum number of pages that can be processed in a single request
CHUNK_SIZE = 5  # Number of pages per chunk for parallel processing

# Add S3 client initialization
s3_client = boto3.client("s3")


# Definition of available tools
tools = [
    {
        "toolSpec": {
            "name": "global_search",
            "description": "Used when the user doesn't specify a document or when the document extension is unclear.",
            "inputSchema": {
                "json": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "The user's query"}
                    },
                    "required": ["query"],
                }
            },
        }
    },
    {
        "toolSpec": {
            "name": "local_search",
            "description": "Used for searching within a specific document when specifies the extension of the document",
            "inputSchema": {
                "json": {
                    "type": "object",
                    "properties": {
                        "filename": {
                            "type": "string",
                            "description": "The specific document filename to search in",
                        },
                        "query": {"type": "string", "description": "The user's query"},
                    },
                    "required": ["filename", "query"],
                }
            },
        }
    },
    {
        "toolSpec": {
            "name": "get_summary",
            "description": "Used for getting document summaries within specific page ranges (maximum 50 pages per request).",
            "inputSchema": {
                "json": {
                    "type": "object",
                    "properties": {
                        "filename": {
                            "type": "string",
                            "description": "The specific document filename to summarize",
                        },
                        "start_page": {
                            "type": "number",
                            "description": "The starting page number to summarize (optional)",
                        },
                        "end_page": {
                            "type": "number",
                            "description": "The ending page number to summarize (optional, must be within 50 pages of start_page)",
                        },
                    },
                    "required": ["filename"],
                }
            },
        }
    },
]


async def global_search(rag_config=None):
    """
    Performs a global search across all documents in the knowledge base

    Args:
        query (str): The search query
        agent_runtime (boto3.client, optional): Bedrock agent runtime client. Defaults to None.
        knowledge_base_id (str, optional): Knowledge base ID. Defaults to None.

    Returns:
        dict: The search results
    """
    try:
        rag_response = await RAGService().call(rag_config.dict())
        return rag_response
    except Exception as e:
        print(f"Error in global search: {type(e).__name__}: {str(e)}")
        raise e


async def local_search(rag_config):
    """
    Performs a search within a specific document using metadata filtering

    Args:
        query (str): The search query
        filename (str): The filename to search within
        agent_runtime (boto3.client, optional): Bedrock agent runtime client. Defaults to None.
        knowledge_base_id (str, optional): Knowledge base ID. Defaults to None.

    Returns:
        dict: The search results filtered to the specific document
    """
    try:
        rag_response = await RAGService().call(rag_config.dict())
        return rag_response
    except Exception as e:
        print(f"Error in local search: {type(e).__name__}: {str(e)}")
        raise e


async def document_cached(rag_config: RAGQueryDTO = {}, bucket_name:str = None):
    
    summary_path = f"summary/resumen_{rag_config.local_document_name}.txt"
    
    # Use the new download_s3 function
    summary_content = download_s3(bucket_name, summary_path)
    
    if summary_content:
        return {
            "retrievalResults": [],  # Empty as we're using cached summary
            "llm_response": summary_content,
            "source": "cached",
        }
    
    # Summary does not exist or error occurred - continue with generation
    return None


def check_rag_response(rag_response):
    """
    Extracts the S3 URI from a RAG response if available.

    Args:
        rag_response (dict): The response from the RAG system.

    Returns:
        str or None: The S3 URI if found, otherwise None.
    """
    results = rag_response.get("retrievalResults", [])

    if not results:
        return None

    location = results[0].get("location", {})
    s3_location = location.get("s3Location", {})

    return s3_location.get("uri")


def calculate_file_pages(s3_parts, bucket_name, file_name):
    """
    Calculates the number of pages of a file based on text files stored in S3.

    Args:
        s3_parts (list): Parts of the file path (e.g., ['s3:', '', 'bucket', 'folder', 'file']).
        bucket_name (str): Name of the S3 bucket.
        file_name (str): Name of the original file (used for logging and error messages).

    Returns:
        tuple: (flag, total_pages) where flag is 1 if pages were found, 0 otherwise.
    """
    if len(s3_parts) < 3:
        print(f"Invalid S3 path: {s3_parts}")
        return 0, 0

    prefix = "/".join(s3_parts[1:-1]) + "/"

    try:
        response = s3_client.list_objects_v2(Bucket=bucket_name, Prefix=prefix)

        contents = response.get("Contents", [])
        total_files = len(contents)

        if total_files == 0:
            print(f"No files found for prefix: {prefix}")
            return 0, 0

        total_pages = total_files // 2  # Assuming 2 files per page

        if total_pages < 1:
            print(f"Less than one page detected for: {file_name}")
            return 0, 0

        return 1, total_pages

    except Exception as e:
        print(f"Error while calculating pages for file '{file_name}': {e}")
        raise ValueError(f"Error calculating number of pages for file: {file_name}") from e


async def get_summary(
    start_page=None, end_page=None, rag_config: RAGQueryDTO = {}, llm=None
):
    """
    Retrieves a summary of a document by filename, using parallel processing for large documents.

    If no page range is provided:
    1. Requests a document sample and extracts S3 URI.
    2. Checks if a cached summary exists in S3.
    3. If not cached, determines total pages and generates the full summary.
    4. Saves the generated summary to S3 for future use (only for complete document summaries).

    Args:
        start_page (int, optional): The start page of the summary range.
        end_page (int, optional): The end page of the summary range.
        rag_config (RAGQueryDTO): RAG configuration with document and query info.
        llm: Language model used to generate summaries.

    Returns:
        dict: Summary including both RAG retrieval results and LLM response.
    """
    # Flag to track if this is a user-specified range
    is_user_specified_range = start_page is not None or end_page is not None

    # Step 1: Call RAG to get document context
    rag_response = await RAGService().call(rag_config.dict())
    s3_uri = check_rag_response(rag_response)
    if not s3_uri:
        raise ValueError(f"Summary: Could not find S3 URI for document: {rag_config.local_document_name}")

    s3_parts = s3_uri.replace("s3://", "").split("/")
    bucket_name = s3_parts[0]

    # Step 2: Check if there's already a cached summary
    if not is_user_specified_range:
        cached = await document_cached(rag_config, bucket_name)
        if cached:
            return cached

        # Step 3: Determine full page range
        start_page, end_page = calculate_file_pages(
            s3_parts=s3_parts,
            bucket_name=bucket_name,
            file_name=rag_config.local_document_name,
        )

        if start_page == 0 and end_page == 0:
            raise ValueError(f"Summary: No chunks found for document: {rag_config.local_document_name}")

    total_pages = end_page

    # Step 4: Choose processing method
    if total_pages > CHUNK_SIZE:
        total_chunks = math.ceil(total_pages / CHUNK_SIZE)
        try:
            chunk_results = await parallel_summary(
                rag_config, total_chunks, CHUNK_SIZE, start_page, end_page, llm
            )
            response = combine_chunk_results(chunk_results)
        except Exception as e:
            raise RuntimeError(f"Error during parallel summary: {type(e).__name__}: {str(e)}") from e
    else:
        try:
            chunk_result = await process_chunk_with_llm(
                rag_config=rag_config,
                start_page=start_page,
                end_page=end_page,
                semaphore=None,
                llm=llm,
            )
            response = {
                "retrievalResults": chunk_result["rag_results"]["retrievalResults"],
                "llm_response": chunk_result["llm_response"],
            }
        except Exception as e:
            raise RuntimeError(f"Error during single chunk summary: {type(e).__name__}: {str(e)}") from e

    # Step 5: Cache result to S3 only if it's not a user-specified range
    if not is_user_specified_range:
        s3_save_document(rag_config, bucket_name, response)

    return response

def s3_save_document(rag_config, bucket_name, response):
    from app.services.utils.helpers.aws.S3 import upload_s3
    
    if bucket_name:
        summary_path = f"summary/resumen_{rag_config.local_document_name}.txt"
        # Convert the response to bytes and upload
        file_content = response["llm_response"].encode("utf-8")
        success = upload_s3(
            bucket_name=bucket_name,
            file=file_content,
            filename=summary_path
        )
        
        if success:
            print(f"Saved summary to s3://{bucket_name}/{summary_path}")
        else:
            print("Failed to save summary to S3")
    else:
        print("Cannot save summary: bucket name not determined OR not whole document")

async def process_tool_call(rag_config: RAGQueryDTO, tool_name: str, tool_input: dict, llm):
    """
    Processes a call to a specific tool with the provided parameters.

    Args:
        rag_config (RAGQueryDTO): RAG configuration containing query parameters.
        tool_name (str): The name of the tool to call.
        tool_input (dict): Input parameters for the tool.
        llm: Language model instance used for summary generation.

    Returns:
        dict: The result of the tool call.
    """
    try:
        match tool_name:
            case TaskTypes.GLOBAL_SEARCH.value:
                rag_config.query = tool_input["query"]
                return await global_search(rag_config)

            case TaskTypes.LOCAL_SEARCH.value:
                rag_config.query = tool_input["query"]
                rag_config.local_document_name = tool_input["filename"]
                return await local_search(rag_config)

            case TaskTypes.SUMMARY.value:
                rag_config.local_document_name = tool_input["filename"]
                start_page = tool_input.get("start_page")
                end_page = tool_input.get("end_page")
                return await get_summary(start_page, end_page, rag_config, llm)

            case _:
                raise ValueError(f"Unknown tool name: {tool_name}")
    except KeyError as e:
        raise ValueError(f"Missing required input parameter: {e}")
