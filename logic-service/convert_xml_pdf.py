from lxml import etree


def extract_clean_text(xml_file, txt_file):
    """Extracts only the text content from XML, removing tags and attributes."""
    parser = etree.XMLParser(encoding="utf-8")  # Ensures parser uses UTF-8
    tree = etree.parse(xml_file, parser)
    root = tree.getroot()

    # Extract only text by removing tags
    text_content = "".join(root.itertext())

    # Clean up unnecessary whitespace
    clean_text = "\n".join(
        line.strip() for line in text_content.splitlines() if line.strip()
    )

    # Save the result to a TXT file with UTF-8 encoding
    with open(txt_file, "w", encoding="utf-8") as f:
        f.write(clean_text)

    print(f"Clean text saved to: {txt_file}")


# Usage example
xml_file = "28122.xml"  # Replace with your XML file
txt_file = "28122.txt"

extract_clean_text(xml_file, txt_file)
