import json
import boto3
import requests
import urllib.parse

s3_client = boto3.client("s3")

API_URL = "http://virtual-agent-backend-service.virtual-agent-backend.svc.cluster.local:8001/ingest/start"


def lambda_handler(event, context):
    print("Event:", json.dumps(event, indent=2))

    for record in event.get("Records", []):
        bucket_name = record["s3"]["bucket"]["name"]
        object_key = urllib.parse.unquote_plus(record["s3"]["object"]["key"])

        if object_key.startswith("raw/"):
            print(f"New file detected: s3://{bucket_name}/{object_key}")

            # Define the payload parameters for the API call
            payload = {
                "parsing_strategy_LLM_configuration": {
                    "model_id": "anthropic.claude-3-sonnet-20240229-v1:0",
                    "anthropic_version": "string",
                    "max_tokens": 1000,
                    "temperature": 0,
                },
                "bucket_name": bucket_name,
                "object_key": object_key,
                "folder_raw_documents": "raw",
                "folder_parsed_documents": "parsed",
                "parsing_strategy_prompt": "parsing_strategy.txt",
                "page_init": 1,
                "batch": None,
            }
            print(payload)
            try:
                response = requests.post(API_URL, json=payload)
                print(f"API Response: {response.status_code} - {response.text}")

            except Exception as e:
                print(f"Error calling API: {e}")

    return {"statusCode": 200, "body": "Event processed successfully"}


{
    "source": ["aws.s3"],
    "detail-type": ["Object Created"],
    "detail": {
        "bucket": {"name": ["sourcedocumentationkb"]},
        "object": {"key": [{"prefix": "notificaction"}]},
    },
}
