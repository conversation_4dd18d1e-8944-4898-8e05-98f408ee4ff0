#/bin/bash
docker build -t ceca-app-virtual:2.4 .
docker tag ceca-app-virtual:2.4 329599625202.dkr.ecr.eu-west-1.amazonaws.com/virtual-agent-container:2.4
aws ecr get-login-password --region eu-west-1 --profile AWSAdministratorAccess-329599625202 | docker login --username AWS --password-stdin 329599625202.dkr.ecr.eu-west-1.amazonaws.com
docker push 329599625202.dkr.ecr.eu-west-1.amazonaws.com/virtual-agent-container:2.4
