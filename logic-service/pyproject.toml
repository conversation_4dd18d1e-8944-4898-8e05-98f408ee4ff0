[tool.poetry]
name = "bedrock-kb-test"
version = "0.1.0"
description = "This project is an AWS Bedrock Retrieval-Augmented Generation (RAG) system. It leverages the capabilities of AWS services to build a robust and scalable RAG solution."
authors = ["<PERSON>. <<EMAIL>>"]
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = "^3.11"
boto3 = "^1.35.82"
loguru = "^0.7.3"
langchain = "^0.3.12"
langchain-core = "^0.3.25"
langchain-aws = "^0.2.9"
fastapi = {extras = ["standard"], version = "^0.115.6"}
pytest = "^8.3.4"
testcontainers = {extras = ["postgres"], version = "^4.9.0"}
pydantic-settings = "^2.7.0"
moto = {extras = ["all", "ec2", "s3"], version = "^5.0.24"}
prefect = "^3.2.7"
pdfplumber = "^0.11.5"
pdf2image = "^1.17.0"
langchain-community = "^0.3.18"
aiofiles = "^24.1.0"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
