from s3_utils import upload_file_to_s3, upload_folder_to_s3, download_file_from_s3


def main():
    bucket_name = "nombre_bucket"

    # Upload a single file
    local_file = "file_name.pdf"
    upload_file_to_s3(local_file, bucket_name)

    # Upload an entire folder
    local_folder = "dataset"
    s3_folder = "s3_dataset"
    upload_folder_to_s3(local_folder, bucket_name, s3_folder)

    # Download a file
    s3_key_to_download = "s3_dataset/file_name.pdf"
    local_download_path = "descargas/file_name.pdf"
    download_file_from_s3(bucket_name, s3_key_to_download, local_download_path)


if __name__ == "__main__":
    main()
