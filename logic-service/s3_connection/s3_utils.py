import os
import boto3


def get_s3_client(profile_name="default"):
    """
    Gets an S3 client using the specified AWS profile.

    Parameters:
        - profile_name (str): Name of the AWS profile to use (defaults to 'default').

    Returns:
        - s3_client: Authenticated S3 client.
    """
    session = boto3.Session(profile_name=profile_name)
    return session.client("s3")


def upload_file_to_s3(
    local_file_path, bucket_name, s3_key=None, profile_name="default"
):
    """
    Uploads a single file to an S3 bucket.

    Parameters:
        - local_file_path (str): Local path of the file to upload.
        - bucket_name (str): Name of the S3 bucket where the file will be uploaded.
        - s3_key (str, optional): Key name that the file will have in S3.
                                 If not provided, the local filename will be used.
        - profile_name (str, optional): AWS profile to use (defaults to 'default').

    Usage example:
        upload_file_to_s3("path/to/file.pdf", "my-bucket", "uploaded_file.pdf")
    """
    if s3_key is None:
        s3_key = os.path.basename(local_file_path)

    s3_client = get_s3_client(profile_name)

    try:
        s3_client.upload_file(local_file_path, bucket_name, s3_key)
        print(
            f"File '{local_file_path}' successfully uploaded to '{bucket_name}/{s3_key}'."
        )
    except Exception as e:
        print(f"Error uploading file {local_file_path} to {bucket_name}: {e}")


def upload_folder_to_s3(
    local_folder_path, bucket_name, s3_folder_name=None, profile_name="default"
):
    """
    Uploads all files from a local folder to an S3 bucket.

    Parameters:
        - local_folder_path (str): Local path of the folder to upload.
        - bucket_name (str): Name of the S3 bucket where files will be uploaded.
        - s3_folder_name (str, optional): Name of the folder in S3 where files will be placed.
                                        If not specified, the local folder name will be used.
        - profile_name (str, optional): AWS profile to use (defaults to 'default').

    Usage example:
        upload_folder_to_s3("dataset", "my-bucket", "s3_dataset")
    """
    if s3_folder_name is None:
        s3_folder_name = os.path.basename(os.path.abspath(local_folder_path))

    if not os.path.isdir(local_folder_path):
        print(f"The path '{local_folder_path}' is not a valid folder.")
        return

    files_list = os.listdir(local_folder_path)
    s3_client = get_s3_client(profile_name)

    for file_name in files_list:
        full_local_path = os.path.join(local_folder_path, file_name)
        if os.path.isfile(full_local_path):
            s3_key = f"{s3_folder_name}/{file_name}"
            try:
                s3_client.upload_file(full_local_path, bucket_name, s3_key)
                print(
                    f"File '{full_local_path}' uploaded as '{s3_key}' in bucket '{bucket_name}'."
                )
            except Exception as e:
                print(f"Error uploading file {full_local_path} to {bucket_name}: {e}")


def download_file_from_s3(bucket_name, s3_key, local_file_path, profile_name="default"):
    """
    Downloads a file from S3 to the specified local path.

    Parameters:
        - bucket_name (str): Name of the S3 bucket to download from.
        - s3_key (str): Key name of the file in S3.
        - local_file_path (str): Local path where the downloaded file will be saved.
        - profile_name (str, optional): AWS profile to use (defaults to 'default').

    Usage example:
        download_file_from_s3("my-bucket", "s3_dataset/file.pdf", "downloads/local_file.pdf")
    """
    s3_client = get_s3_client(profile_name)

    try:
        s3_client.download_file(bucket_name, s3_key, local_file_path)
        print(
            f"File '{s3_key}' successfully downloaded from '{bucket_name}' to '{local_file_path}'."
        )
    except Exception as e:
        print(f"Error downloading file {s3_key} from {bucket_name}: {e}")
