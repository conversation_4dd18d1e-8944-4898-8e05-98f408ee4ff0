# from app.services.drivers.embeddings.native.aws.embedding import *
# # # from app.routes.embedding_controller.langchain.aws import AWSLangchainEmbedding
# # # from app.routes.llm_driver.llm_base import *

# # import json
# # import boto3

# # import json
# # import boto3

# # import json
# # import boto3

# # import json
# # import boto3


# # # Ejemplo de uso
# # # update_all_json_in_s3('data-ingestion-cecabank', 'parsed/txt/')


# # # Ejemplo de uso
# # #

# if __name__ == "__main__":
#     #TEST AWS NATIVE
#     # embedding = AWSEmbedding(region="eu-west-1", model_id="amazon.titan-embed-text-v2:0")
#     # result = embedding.get_embedding("Hello World")
#     # print(result)

# #     ##TEST AWS LANGCHAIN
# #     # embedding = AWSLangchainEmbedding(region="eu-central-1", model_id="amazon.titan-embed-text-v2:0")
# #     # result = embedding.get_embedding("Hello World")
# #     # print(result)

# #     ##TEST AWS BEDROCK LLM CALL
# #     # import asyncio
# #     # from fastapi.responses import StreamingResponse

#     # llm = LLMService(BedrockLLM())
#     # messages = [
#     #     {"role": "user", "content": "haz un resumen en 20 lineas del quijote."},
#     # ]
#     # response = asyncio.run(
#     #     llm.invoke_model(
#     #         model_id="anthropic.claude-instant-v1",
#     #         streaming=False,
#     #         messages=messages,
#     #         system_prompt="responde en inglés",
#     #     )
#     # )
#     # response_body = json.loads(response.get("body").read())
#     # print(response_body.get("content")[0]["text"])

# #     # import asyncio

# #     # messages = [
# #     #     {"role": "user", "content": "haz un resumen en 20 lineas del quijote."},
# #     # ]
# #     # llm = LLMService(BedrockLLM())
# #     # response = asyncio.run(
# #     #     llm.invoke_model(
# #     #         model_id="anthropic.claude-instant-v1",
# #     #         streaming=True,
# #     #         messages=messages,
# #     #         system_prompt="responde en inglés",
# #     #     )
# #     # )
# #     # stream = response.get("body")
# #     # if stream:
# #     #     for event in stream:
# #     #         chunk = event.get("chunk")
# #     #         if chunk:
# #     #             print(json.loads(chunk.get("bytes").decode()))

# # async def tarea1():
# #     from app.services.drivers.ingest.native.aws import parsing_strategy

# #     await parsing_strategy.pdf_to_pgvector_pipeline(
# #         bucket_name="data-ingestion-cecabank",
# #         folder_raw_documents="raw_documents",
# #         folder_parsed_documents="parsed",
# #         parsing_strategy="parsing_strategy.txt",
# #         page_init=1,
# #         model_id="anthropic.claude-3-5-sonnet-********-v1:0",
# #     )


# # async def main():
# #     await asyncio.gather(tarea1())


# # asyncio.run(main())

import asyncio
import logging
import boto3
import base64
from pathlib import Path
from prefect import flow

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

s3 = boto3.client("s3")


@flow
def process_metadata():
    from app.services.drivers.ingest.native.aws import parsing_strategy
    from app.services.drivers.ingest.native.aws.parsing_strategy import (
        build_and_upload_metadata,
    )

    try:
        # Configuración
        bucket_name = "cecabank-docs"
        file_name = "BOE-A-2024-22928.pdf"
        folder_parsed_documents = "parsed"

        # Obtener las imágenes ya procesadas del S3
        prefix = f"{folder_parsed_documents}/images/{file_name}/"
        images = []

        # Cargar las imágenes desde S3 si existen, o usar las del sistema de archivos local
        try:
            response = s3.list_objects_v2(Bucket=bucket_name, Prefix=prefix)
            if "Contents" in response:
                for obj in sorted(response["Contents"], key=lambda x: x["Key"]):
                    img_data = s3.get_object(Bucket=bucket_name, Key=obj["Key"])[
                        "Body"
                    ].read()
                    encoded_image = base64.b64encode(img_data).decode()
                    images.append(encoded_image)
            logger.info(f"Loaded {len(images)} images from S3")
        except Exception as e:
            logger.error(f"Error loading images from S3: {e}")
            raise

        # Ejecutar solo la tarea de metadata
        build_and_upload_metadata(
            parsing_strategy="parsing_strategy.txt",
            images=images,
            file_name=file_name,
            bucket_name=bucket_name,
            folder_parsed_documents=folder_parsed_documents,
            page_init=1,
            model_id="anthropic.claude-3-sonnet-20240229-v1:0",  # Cambiado a claude-v2 que es más estable
        )
    except Exception as e:
        logger.error(f"Error in pipeline: {str(e)}", exc_info=True)
        raise


if __name__ == "__main__":
    process_metadata()
