#!/usr/bin/env python3
"""
Script de prueba para verificar la comunicación entre logic-service y embedding-service
"""

import asyncio
import httpx
import json

# Configuración de URLs
LOGIC_SERVICE_URL = "http://localhost:8000"
EMBEDDING_SERVICE_URL = "http://localhost:8001"

# URLs que el logic-service podría estar usando
POSSIBLE_EMBEDDING_URLS = [
    "http://localhost:8001",
    "http://embedding-service:8001",
    "http://127.0.0.1:8001"
]

# Datos de prueba
RAG_PAYLOAD = {
    "kb_id": "KGESVB3EJX",
    "top_k": 5,
    "local_document_name": "",
    "region": "eu-west-1",
    "query": "Quien es sancho panza",
    "model_id": "amazon.titan-embed-text-v2:0",
    "kb_type": "aws",
    "postgres_config": {
        "page_start": 0,
        "page_end": 0,
        "table_name": "knowledge_base"
    },
    "filters": {}
}

CHAT_PAYLOAD = {
    "query": {
        "user_query": "Quien es sancho panza"
    },
    "agent_type": "agent",
    "llm": {
        "model_id": "anthropic.claude-3-sonnet-20240229-v1:0",
        "anthropic_version": "string",
        "max_tokens": 0,
        "temperature": 0
    },
    "rag": {
        "kb_id": "KGESVB3EJX",
        "top_k": 5,
        "local_document_name": "",
        "region": "eu-west-1",
        "model_id": "amazon.titan-embed-text-v2:0",
        "kb_type": "aws",
        "query": "",
        "postgres_config": {
            "page_start": 0,
            "page_end": 0,
            "table_name": "knowledge_base"
        },
        "filters": {}
    },
    "history": [],
    "guardrail_id": "66qbpt9jl1f7"
}

async def test_embedding_service_direct():
    """Prueba directa del embedding service"""
    print("🔍 Probando embedding service directamente...")

    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                f"{EMBEDDING_SERVICE_URL}/rag",
                json=RAG_PAYLOAD,
                timeout=30.0
            )

            print(f"Status Code: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                results = data.get("retrievalResults", [])
                print(f"✅ Embedding service funciona correctamente")
                print(f"   Resultados obtenidos: {len(results)}")
                if results:
                    print(f"   Primer resultado contiene: {results[0].get('content', {}).get('text', '')[:100]}...")
                return True
            else:
                print(f"❌ Error en embedding service: {response.status_code}")
                print(f"   Response: {response.text}")
                return False

        except Exception as e:
            print(f"❌ Error conectando con embedding service: {e}")
            return False

async def test_logic_service_chat():
    """Prueba del logic service con chat/converse"""
    print("\n💬 Probando logic service con chat/converse...")

    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                f"{LOGIC_SERVICE_URL}/chat/converse",
                json=CHAT_PAYLOAD,
                timeout=60.0
            )

            print(f"Status Code: {response.status_code}")

            if response.status_code == 200:
                print("✅ Logic service responde correctamente")

                # Como es streaming, vamos a leer las primeras líneas
                content = response.text
                lines = content.split('\n')[:10]  # Primeras 10 líneas

                print("   Primeras líneas de respuesta:")
                for i, line in enumerate(lines):
                    if line.strip():
                        print(f"   {i+1}: {line}")

                return True
            else:
                print(f"❌ Error en logic service: {response.status_code}")
                print(f"   Response: {response.text}")
                return False

        except Exception as e:
            print(f"❌ Error conectando con logic service: {e}")
            return False

async def test_connectivity():
    """Prueba la conectividad a diferentes URLs del embedding service"""
    print("\n🔗 Probando conectividad a diferentes URLs...")

    for url in POSSIBLE_EMBEDDING_URLS:
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{url}/docs", timeout=5.0)
                if response.status_code == 200:
                    print(f"   ✅ {url} - Accesible")
                else:
                    print(f"   ⚠️  {url} - Responde pero con código {response.status_code}")
        except Exception as e:
            print(f"   ❌ {url} - No accesible: {type(e).__name__}")

async def main():
    print("🚀 Iniciando pruebas de comunicación entre servicios\n")

    # Prueba 0: Conectividad
    await test_connectivity()

    # Prueba 1: Embedding service directo
    embedding_ok = await test_embedding_service_direct()

    # Prueba 2: Logic service con chat
    logic_ok = await test_logic_service_chat()

    print("\n📊 Resumen de pruebas:")
    print(f"   Embedding Service: {'✅ OK' if embedding_ok else '❌ FAIL'}")
    print(f"   Logic Service:     {'✅ OK' if logic_ok else '❌ FAIL'}")

    if embedding_ok and not logic_ok:
        print("\n🔍 El embedding service funciona pero el logic service no.")
        print("   Esto sugiere un problema en la comunicación entre servicios.")
        print("   Revisa los logs del logic service para más detalles.")
        print("\n💡 Posibles soluciones:")
        print("   1. Verifica que la variable de entorno EMBEDDING_BASE_URL esté configurada")
        print("   2. Si ejecutas localmente, usa: export EMBEDDING_BASE_URL=http://localhost:8001")
        print("   3. Revisa los logs del logic service para ver qué URL está intentando usar")
    elif not embedding_ok:
        print("\n🔍 El embedding service no funciona.")
        print("   Revisa la configuración y los logs del embedding service.")
    elif embedding_ok and logic_ok:
        print("\n🎉 Ambos servicios funcionan correctamente!")

if __name__ == "__main__":
    asyncio.run(main())
